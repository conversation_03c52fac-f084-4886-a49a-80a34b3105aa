# 系统功能设计文档

## 1. 业务架构

### 1.1 业务模块关系图

*这里使用mermaid流程图描述各模块间的层级关系*

```mermaid
flowchart TD
    A(A模块) --> B(B模块)
    A(A模块) --> C(C模块)
    B(B模块) --> D(D模块)
```

### 1.2 模块列表

*描述各模块的基础信息（编号、名称、英文名、英文缩写）*
*编号：文档内唯一标识，不同文档可以重复，编号以MD开头+4位数字（从0001开始）*
*名称：模块的中文名称*
*英文名：模块的英文名称，也是缩写的全称*
*英文缩写：用于生成代码时，命名包路径，如，com.xinlong.alm.ast*

| 模块编号   | 模块名称   | 模块英文名        | 英文缩写 |
| ------ | ------ | ------------ | ---- |
| MD0001 | 资产宽表模块 | asset_details | ast  |

### 1.3 数据模型

#### 1.3.1 资产宽表模块

*描述资产宽表模块下的表间关系及表属性信息*

##### 1.3.1.1 表间关系

*表间关系用mermaid图描述，主要用于梳理关系，使用英文描述，中文字符会报错*

```mermaid
erDiagram
    t_ast_asset_definition ||--o{ t_ast_asset_detail_overall : "provides basic asset info"
    t_ast_account_name_map ||--o{ t_ast_asset_detail_overall : "maps account names"
    t_ast_asset_basic_config ||--o{ t_ast_asset_detail_overall : "provides asset config"
    t_ast_asset_allocation_category ||--o{ t_ast_asset_detail_overall : "provides asset classification"
    t_ast_asset_liquidity_coeff ||--o{ t_ast_asset_detail_overall : "provides liquidity info"
    t_ast_credit_rating_map ||--o{ t_ast_asset_detail_overall : "maps credit ratings"
    t_ast_bank_classification_map ||--o{ t_ast_asset_detail_overall : "provides bank classification"
    t_ast_payment_method_map ||--o{ t_ast_asset_detail_overall : "maps payment methods"
    t_ast_discount_curve_config ||--o{ t_ast_asset_detail_overall : "provides discount curve config"
    t_ast_fixed_income_term_cat ||--o{ t_ast_asset_detail_overall : "provides term classification"
    t_ast_wind_industry ||--o{ t_ast_asset_detail_overall : "provides industry info"
    t_ast_wind_rating ||--o{ t_ast_asset_detail_overall : "provides wind ratings"
    t_ast_account_holding ||--o{ t_ast_asset_detail_overall : "provides market value"
    t_ast_three_account_holding ||--o{ t_ast_asset_detail_overall : "provides holding details"
    t_ast_var_analysis ||--o{ t_ast_asset_detail_overall : "provides var analysis"
```

**核心关系说明：**
- **t_ast_asset_detail_overall** 是核心汇聚表，从多个基础表和映射表获取数据
- **t_ast_asset_definition** 提供基础资产信息，是主要的数据源
- 其他映射表和配置表提供数据转换、分类和补充功能
- 外部持仓表提供实时的持仓和市值数据

##### 1.3.1.2 表名字典

*列出资产宽表模块所有表信息*

| 表编号    | 表中文名     | 表英文名                      | 备注      |
| ------ | -------- | ------------------------- | ------- |
| TB0001 | 组合持仓表    | t_ast_account_holding     | 表结构未确定  |
| TB0002 | 三账户持仓表   | t_ast_three_account_holding | 表结构未确定  |
| TB0003 | VaR值分析表  | t_ast_var_analysis        | 表结构未确定  |
| TB0004 | Wind行业表   | t_ast_wind_industry       |         |
| TB0005 | Wind评级表   | t_ast_wind_rating           |         |
| TB0006 | 资产定义表    | t_ast_asset_definition      |         |
| TB0007 | 账户名称映射表  | t_ast_account_name_map      |         |
| TB0008 | 资产基础配置表  | t_ast_asset_basic_config    |         |
| TB0009 | 资产配置状况分类表 | t_ast_asset_allocation_category |         |
| TB0010 | 资产流动性分类及变现系数表 | t_ast_asset_liquidity_coeff |         |
| TB0011 | 信用评级映射表 | t_ast_credit_rating_map |         |
| TB0012 | 银行分类映射表 | t_ast_bank_classification_map |         |
| TB0013 | 付息方式映射表 | t_ast_payment_method_map |         |
| TB0014 | 折现曲线配置表 | t_ast_discount_curve_config |         |
| TB0015 | 固收资产剩余期限资产分类表 | t_ast_fixed_income_term_cat |         |
| TB0016 | 整体资产明细表 | t_ast_asset_detail_overall |         |


##### 1.3.1.3 表集

*描述详细的表属性信息，id（默认为主键）、create_time、update_time、create_by、update_by、is_del等字段不需要描述，后续生成DDL时会自动补充*
*唯一索引：这里唯一索引是指业务字段单字段或多字段组合的唯一性，如多字段组合的情况，在所有字段唯一索引列设置为“是”*
*说明：描述字段的作用，枚举类型字段格式为描述,value1:label1[,...,valueN:labelN]，这里一定要注意格式，在后续开发步骤中会通过这里的字段枚举描述生成字段数据*

**（1）TB0001 - 组合持仓表**

*存储组合持仓的扁平化数据，只保留核心业务字段*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                        |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）      |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称,如传统、分红、万能、投连等原始文本,同时作为顶层分类使用 |
| **security_code**      | varchar | 20    | 否   | 是    | 无   | 证券标识代码,如0003956CK、173996等 |
| **asset_name**         | varchar | 200   | 否   | 是    | 无   | 资产名称,如中国农业银行股份有限公司、23重庆04等 |
| market_value           | decimal | 30,10 | 是   | 否    | 0   | 市值,如********.44、*********.6等 |

**（2）TB0002 - 三账户持仓表**

*存储三账户持仓的详细数据，只导入有证券代码的明细行，去除汇总行*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                        |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）      |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 维度名称1,账户名称,如传统、分红等 |
| **security_code**      | varchar | 20    | 否   | 是    | 无   | 证券代码,如0003956CK、173996等 |
| security_name          | varchar | 200   | 是   | 否    | 无   | 证券简称,如中国农业银行股份有限公司、23重庆04等 |
| dimension_name_2       | varchar | 100   | 是   | 否    | 无   | 维度名称2,如固定收益类投资资产 |
| dimension_name_3       | varchar | 100   | 是   | 否    | 无   | 维度名称3,如境内资产 |
| dimension_name_4       | varchar | 100   | 是   | 否    | 无   | 维度名称4,如传统固定收益类投资资产 |
| dimension_name_5       | varchar | 100   | 是   | 否    | 无   | 维度名称5,如存款、政府债券 |
| dimension_name_6       | varchar | 100   | 是   | 否    | 无   | 维度名称6,如具体分类标识 |
| holding_quantity       | decimal | 30,10 | 是   | 否    | 0   | 持仓数量 |
| holding_face_value     | decimal | 30,10 | 是   | 否    | 0   | 持仓面值 |
| cost                   | decimal | 30,10 | 是   | 否    | 0   | 成本 |
| net_cost               | decimal | 30,10 | 是   | 否    | 0   | 净价成本 |
| net_market_value       | decimal | 30,10 | 是   | 否    | 0   | 净价市值 |
| market_value           | decimal | 30,10 | 是   | 否    | 0   | 市值 |
| coupon_rate            | decimal | 10,6  | 是   | 否    | 0   | 票面利率 |
| annual_payment_frequency | int   | 5     | 是   | 否    | 0   | 年付息次数 |
| value_date             | date    | 10    | 是   | 否    | 无   | 起息日 |
| maturity_date          | date    | 10    | 是   | 否    | 无   | 到期日 |
| accounting_type        | varchar | 50    | 是   | 否    | 无   | 会计类型,如可供出售类、其他类 |
| latest_purchase_date   | date    | 10    | 是   | 否    | 无   | 最新买入日期 |
| entity_rating          | varchar | 10    | 是   | 否    | 无   | 主体外部评级 |
| security_rating        | varchar | 10    | 是   | 否    | 无   | 证券外部评级 |

**（3）TB0003 - VaR值分析表**

*存储四种不同类型的VaR值分析数据，支持通过证券代码匹配，包含证券代码提取和映射逻辑*

**业务说明**：
- **数据来源**：业务会导入4张不同的源数据表
- **数据类型**：通过data_type字段区分四种VaR分析类型
  - 债基1年：债基1年VAR值表数据
  - 债基3年：债基3年VAR值表数据
  - 权益1年：权益1年VAR值表数据
  - 权益3年：权益3年VAR值表数据
- **导入界面**：设计4个不同的导入按钮，每个按钮对应一个data_type值
- **数据查询**：通过data_type字段可以轻松筛选和查询不同类型的VAR数据

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                        |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）      |
| **data_type**          | varchar | 20    | 否   | 是    | 无   | 数据类型,债基1年、债基3年、权益1年、权益3年 |
| **account_name**       | varchar | 100   | 否   | 是    | 无   | 账户名称,从维度名称中提取,如传统 |
| **security_code**      | varchar | 20    | 是   | 是    | 无   | 证券代码,从维度名称中提取或通过资产名称匹配TB0006获取 |
| dimension_name         | varchar | 200   | 否   | 否    | 无   | 原始维度名称,如001045OF--华夏可转债增强债券A |
| asset_name             | varchar | 200   | 是   | 否    | 无   | 资产名称,从维度名称中提取 |
| period_date            | varchar | 8     | 否   | 否    | 无   | 周期,格式YYYYMMDD |
| market_value           | decimal | 30,10 | 是   | 否    | 0   | 市值金额 |
| var_amount             | decimal | 30,10 | 是   | 否    | 0   | VaR金额 |
| incremental_var_amount | decimal | 30,10 | 是   | 否    | 0   | 增量VaR金额 |
| cte_percentage         | decimal | 10,6  | 是   | 否    | 0   | CTE百分比 |
| cte_amount             | decimal | 30,10 | 是   | 否    | 0   | CTE金额 |
| component_var_percentage | decimal | 10,6  | 是   | 否    | 0   | 成份VaR百分比 |
| component_var_contribution_percentage | decimal | 10,6  | 是   | 否    | 0   | 成份VaR贡献百分比 |
| component_var_amount   | decimal | 30,10 | 是   | 否    | 0   | 成分VaR金额 |

**（4）TB0004 - Wind行业表**

| 字段名                    | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                        |
| ---------------------- | ------- | --- | --- | ---- | --- | ------------------------- |
| **accounting_period**  | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）      |
| **security_code**      | varchar | 20  | 否   | 是    | 无   | 证券标识代码                    |
| security_name          | varchar | 100 | 否   | 否    | 无   | 证券名称                      |
| industry               | varchar | 50  | 否   | 否    | 无   | Wind行业分类                  |

**（5）TB0005 - Wind评级表**

| 字段名                    | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                        |
| ---------------------- | ------- | --- | --- | ---- | --- | ------------------------- |
| **accounting_period**  | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）      |
| **security_code**      | varchar | 20  | 否   | 是    | 无   | 证券标识代码                    |
| security_name          | varchar | 100 | 否   | 否    | 无   | 证券简称                      |
| entity_rating          | varchar | 10  | 是   | 否    | 无   | 主体评级,与TB0006中的credit_rating字段使用相同的字典数据                      |
| bond_rating            | varchar | 10  | 是   | 否    | 无   | 债项评级,与TB0006中的credit_rating字段使用相同的字典数据                      |

**（6）TB0006 - 资产定义表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **account_name**           | varchar | 50  | 否   | 是    | 无   | 原始账户名称,存储实际的原始文本,如传统、分红、万能、投连、资本补充债等                                                                                                                                                                                                                                                                                                      |
| **asset_name**             | varchar | 200 | 否   | 是    | 无   | 资产名称                                                                                                                                                                                                                                                                                                                                             |
| **security_code**          | varchar | 20  | 否   | 是    | 无   | 证券标识代码                                                                                                                                                                                                                                                                                                                                           |
| asset_sub_sub_category     | varchar | 50  | 否   | 否    | 无   | 资产小小类,01:存款,02:政府债券,03:中期票据,04:债券型基金,05:公募基金固定收益类专户,06:公司债企业债,07:固定收益类保险资产管理产品,08:货币类保险资产管理产品,09:不动产债权投资计划,10:基础设施债权投资计划,11:信托计划,12:上市普通股票,13:证券投资基金,14:可转债,15:以自有资金对保险类企业的股权投资,16:不含保证条款的股权投资计划、私募股权投资基金,17:权益类和混合类保险资管产品,18:权益类信托计划,19:不动产相关金融产品,20:权益类基金专户产品,21:REITS,22:融资回购,23:活期存款,24:回购,25:负债,26:其他,27:项目资产支持计划,28:短期、超短期融资券,29:其他普通未上市股权 |
| domestic_foreign           | varchar | 20  | 否   | 否    | 无   | 境内外标识,01:境内资产,02:境外资产,03:0（表示没有境内外这个属性）                                                                                                                                                                                                                                                                                                          |
| credit_rating              | varchar | 10  | 是   | 否    | 无   | 信用评级,01:AA,02:AA+,03:AAA,04:不涉及,05:免评级,06:无评级                                                                                                                                                                                                                                                                                                    |
| industry_category          | varchar | 50  | 是   | 否    | 无   | 所属行业分类                                                                                                                                                                                                                                                                                                                                           |
| alm_asset_name             | varchar | 100 | 是   | 否    | 无   | ALM资产名称                                                                                                                                                                                                                                                                                                                                          |
| five_level_classification  | varchar | 20  | 是   | 否    | 无   | 五级分类,01:正常类,02:关注类,03:次级类,04:可疑类,05:损失类,06:不良类                                                                                                                                                                                                                                                                                                   |

**（7）TB0007 - 账户名称映射表**

| 字段名                    | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                        |
| ---------------------- | ------- | --- | --- | ---- | --- | ------------------------- |
| **accounting_period**  | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）      |
| **account_name**       | varchar | 50  | 否   | 是    | 无   | 原始账户名称,存储实际的原始文本,如传统、分红、万能、投连、资本补充债等 |
| account_name_mapping   | varchar | 50  | 否   | 否    | 无   | 映射后的标准账户名称,01:传统账户,02:分红账户,03:万能账户,04:独立账户,05:资本补充债账户,06:普通账户,需要创建新的字典数据 |

**（8）TB0008 - 资产基础配置表**

| 字段名                              | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                     |
| -------------------------------- | ------- | --- | --- | ---- | --- |----------------------------------------------------------------------------------------|
| **accounting_period**            | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                   |
| **sequence_number**              | int     | 10  | 否   | 是    | 无   | 配置序号                                                                                   |
| **asset_sub_sub_category**       | varchar | 50  | 否   | 是    | 无   | 资产小小类,与TB0006中的asset_sub_sub_category字段保持一致                                            |
| fixed_income_sub_category        | varchar | 50  | 是   | 否    | 无   | 固收资产细分类,01:传统固定收益类投资资产,02:非标准固定收益类投资资产,03:其他固定收益类金融产品,需要创建新的字典数据                       |
| calculable_cashflow_flag         | char    | 5   | 否   | 否    | '0' | 可计算现金流标识,0:否,1:是                                                                       |
| credit_rating_logic_flag         | char    | 5   | 否   | 否    | '0' | 信用评级取值逻辑标识                                                                             |
| industry_statistics_flag         | varchar | 20  | 否   | 否    | 无   | 行业统计标识,01:不考虑,02:银行,03:非银金融,04:wind中获取,05:房地产,06:建筑装饰,需要创建新的字典数据                       |
| single_asset_statistics_flag     | varchar | 20  | 否   | 否    | 无   | 单一资产统计标识,01:不考虑,02:考虑,需要创建新的字典数据                                                       |
| five_level_statistics_flag       | varchar | 20  | 否   | 否    | 无   | 五级分类资产统计标识,01:不考虑,02:债券,03:其他,04:基础设施及不动产债权投资计划,05:股权-无公允价格,06:股权金融产品,07:股权,需要创建新的字典数据 |
| spread_duration_statistics_flag  | char    | 5   | 否   | 否    | '0' | 利差久期资产统计标识,0:否,1:是                                                                     |

**（9）TB0009 - 资产配置状况分类表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_sub_sub_category** | varchar | 50  | 是   | 是    | 无   | 资产小小类,与TB0006中的asset_sub_sub_category字段保持一致,分类层级记录时为空                                                                                                                                                                                                                                                                                           |
| **domestic_foreign**       | varchar | 20  | 是   | 是    | 无   | 境内外标识,与TB0006中的domestic_foreign字段保持一致,分类层级记录时为空                                                                                                                                                                                                                                                                                                  |
| **category_id**            | varchar | 20  | 否   | 是    | 无   | 分类唯一标识                                                                                                                                                                                                                                                                                                                                           |
| category_name              | varchar | 100 | 否   | 否    | 无   | 分类名称                                                                                                                                                                                                                                                                                                                                             |
| parent_category_id         | varchar | 20  | 是   | 否    | 无   | 父级分类ID,顶级分类为空                                                                                                                                                                                                                                                                                                                                    |
| category_level             | int     | 5   | 否   | 否    | 无   | 分类级别,1:资产大类,2:资产一级分类,3:资产二级分类,4:资产三级分类,5:资产小小类映射                                                                                                                                                                                                                                                                                               |

**（10）TB0010 - 资产流动性分类及变现系数表**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_sub_sub_category** | varchar | 50    | 否   | 是    | 无   | 资产小小类,与TB0006中的asset_sub_sub_category字段保持一致                                                                                                                                                                                                                                                                                                      |
| **bond_type**              | varchar | 20    | 是   | 是    | 无   | 债券类型,01:国债,02:政府债,03:准政府债,04:政策性银行金融债,05:金融企业债,06:非金融企业债,需要创建新的字典数据                                                                                                                                                                                                                                                                           |
| **accounting_classification** | varchar | 20    | 是   | 是    | 无   | 会计分类类型,01:交易类,02:可供出售类,03:持有到期类,需要创建新的字典数据                                                                                                                                                                                                                                                                                                     |
| **credit_rating**          | varchar | 10    | 是   | 是    | 无   | 信用评级,与TB0006中的credit_rating字段保持一致                                                                                                                                                                                                                                                                                                                |
| asset_liquidity_category   | varchar | 20    | 否   | 否    | 无   | 资产流动性分类,01:现金及流动性管理工具,02:高流动性资产,03:中低流动性资产,04:不涉及,需要创建新的字典数据                                                                                                                                                                                                                                                                                 |
| realization_coefficient    | decimal | 10,2  | 否   | 否    | 0   | 变现系数百分比,如100表示100%                                                                                                                                                                                                                                                                                                                                |

**（11）TB0011 - 信用评级映射表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **credit_rating**          | varchar | 10  | 否   | 是    | 无   | 原始信用评级,与TB0006中的credit_rating字段使用相同的字典数据                                                                                                                                                                                                                                                                                                        |
| credit_rating_table_used   | varchar | 10  | 否   | 否    | 无   | 信用评级表使用的评级,与TB0006中的credit_rating字段使用相同的字典数据                                                                                                                                                                                                                                                                                                    |
| discount_curve_rating      | varchar | 10  | 否   | 否    | 无   | 折现曲线使用评级,与TB0006中的credit_rating字段使用相同的字典数据                                                                                                                                                                                                                                                                                                        |

**（12）TB0012 - 银行分类映射表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **alm_asset_name**         | varchar | 100 | 否   | 是    | 无   | ALM资产名称,与TB0006中的alm_asset_name字段保持一致                                                                                                                                                                                                                                                                                                            |
| bank_classification        | varchar | 50  | 否   | 否    | 无   | 银行分类,01:国有商业银行,02:股份制商业银行、邮政储蓄银行,需要创建新的字典数据                                                                                                                                                                                                                                                                                                   |

**（13）TB0013 - 付息方式映射表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **annual_payment_frequency** | int     | 5   | 否   | 是    | 无   | 年付息次数                                                                                                                                                                                                                                                                                                                                           |
| payment_method             | varchar | 20  | 否   | 否    | 无   | 付息方式描述,01:到期支付,02:按年支付,03:按半年支付,04:按季支付,需要创建新的字典数据                                                                                                                                                                                                                                                                                           |

**（14）TB0014 - 折现曲线配置表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_sub_sub_category** | varchar | 50  | 否   | 是    | 无   | 资产小小类,与TB0006中的asset_sub_sub_category字段保持一致                                                                                                                                                                                                                                                                                                      |
| **discount_curve_rating**  | varchar | 10  | 是   | 是    | 无   | 折现曲线使用评级,与TB0011中的discount_curve_rating字段保持一致                                                                                                                                                                                                                                                                                                    |
| discount_curve_flag        | int     | 5   | 否   | 否    | 无   | 折现曲线标识,0:适用到期收益率计算方法,1:国债曲线,2:企业债AAA曲线,3:企业债AA+曲线,4:企业债AA曲线,需要创建新的字典数据                                                                                                                                                                                                                                                                     |
| remark                     | varchar | 100 | 是   | 否    | 无   | 备注信息                                                                                                                                                                                                                                                                                                                                             |

**（15）TB0015 - 固收资产剩余期限资产分类表**

| 字段名                        | 数据类型    | 长度  | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | --- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **account_period**         | varchar | 6   | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_sub_sub_category** | varchar | 50  | 否   | 是    | 无   | 资产小小类,与TB0006中的asset_sub_sub_category字段保持一致                                                                                                                                                                                                                                                                                                      |
| **domestic_foreign**       | varchar | 20  | 否   | 是    | 无   | 境内外标识,与TB0006中的domestic_foreign字段保持一致                                                                                                                                                                                                                                                                                                            |
| **bond_type**              | varchar | 20  | 是   | 是    | 无   | 债券类型,与TB0010中的bond_type字段保持一致                                                                                                                                                                                                                                                                                                                   |
| fixed_income_term_category | varchar | 50  | 否   | 否    | 无   | 固收资产剩余期限分类,01:存款,02:（准）政府债券,03:金融企业（公司）债券,04:非金融企业（公司）债券,05:非标准固定收益类投资资产,06:其他固定收益类金融产品,07:境外固定收益类投资资产,需要创建新的字典数据                                                                                                                                                                                                                   |

**（16）TB0016 - 整体资产明细表**

*这是一个综合性的资产明细表，通过关联多个基础表生成完整的资产信息，字段较多，分为基础信息、评级信息、持仓信息、期限信息、分类信息等几个部分*

**基础信息字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_number**           | int     | 11    | 否   | 是    | 无   | 资产编号                                                                                                                                                                                                                                                                                                                                             |
| **account_name**           | varchar | 50    | 否   | 是    | 无   | 账户名称,如传统账户、分红账户等,通过TB0007账户名称映射表转换,匹配条件：资产定义表.账户名称=账户名称映射表.账户名称                                                                                                                                                                                                                                                                      |
| **security_code**          | varchar | 20    | 是   | 是    | 无   | 证券标识代码,来源于TB0006资产定义表                                                                                                                                                                                                                                                                                                                            |
| asset_name                 | varchar | 100   | 否   | 否    | 无   | 资产具体名称,来源于TB0006资产定义表                                                                                                                                                                                                                                                                                                                            |
| asset_sub_sub_category     | varchar | 50    | 是   | 否    | 无   | 资产小小类,来源于TB0006资产定义表                                                                                                                                                                                                                                                                                                                             |
| asset_major_category       | varchar | 50    | 否   | 否    | 无   | 资产大类,通过TB0009资产配置状况分类表匹配,匹配条件：资产定义表.资产小小类=资产配置状况分类表.资产小小类                                                                                                                                                                                                                                                                                |
| domestic_foreign           | varchar | 20    | 是   | 否    | 无   | 境内外标识,来源于TB0006资产定义表                                                                                                                                                                                                                                                                                                                             |

**分类信息字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| fixed_income_sub_category  | varchar | 50    | 是   | 否    | 无   | 固定收益资产细分,通过TB0008资产基础配置表匹配,匹配条件：资产定义表.资产小小类=资产基础配置表.资产小小类                                                                                                                                                                                                                                                                              |
| asset_allocation_level1    | varchar | 100   | 是   | 否    | 无   | 资产配置状况一级分类,1.如果资产小小类=融资回购,通过TB0009匹配;2.如果资产小小类<>融资回购,匹配条件：资产小小类、境内外                                                                                                                                                                                                                                                                  |
| asset_allocation_level2    | varchar | 100   | 是   | 否    | 无   | 资产配置状况二级分类,1.如果资产小小类=政府债券且剩余期限<=1,通过TB0009匹配;2.如果政府债券且剩余期限>1,为空值;3.其他情况通过TB0009匹配                                                                                                                                                                                                                                                    |
| asset_allocation_level3    | varchar | 100   | 是   | 否    | 无   | 资产配置状况三级分类,通过TB0009资产配置状况分类表匹配,匹配条件：资产小小类、境内外                                                                                                                                                                                                                                                                                            |

**评级信息字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| wind_entity_rating         | varchar | 10    | 是   | 否    | 无   | Wind系统中的主体评级,通过TB0005Wind评级表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                     |
| wind_debt_rating           | varchar | 10    | 是   | 否    | 无   | Wind系统中的债项评级,通过TB0005Wind评级表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                     |
| risk_entity_rating         | varchar | 10    | 是   | 否    | 无   | 风控绩效系统主体评级,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                      |
| risk_debt_rating           | varchar | 10    | 是   | 否    | 无   | 风控绩效系统债项评级,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                      |
| credit_rating_maintenance  | varchar | 10    | 是   | 否    | 无   | 信用评级维护表评级,直接从TB0006资产定义表的信用评级字段获取                                                                                                                                                                                                                                                                                                                |
| credit_rating_logic_flag   | varchar | 5     | 是   | 否    | 无   | 信用评级取值逻辑标识,通过TB0008资产基础配置表匹配,匹配条件：资产小小类                                                                                                                                                                                                                                                                                                    |
| credit_rating              | varchar | 10    | 是   | 否    | 无   | 最终信用评级,根据信用评级取值逻辑标识计算：0=0,1=无评级,2=免评级,3=使用风控绩效系统评级字段,4=使用Wind评级字段,5=使用信用评级维护表评级字段                                                                                                                                                                                                                                                 |
| credit_rating_category     | varchar | 20    | 是   | 否    | 无   | 信用评级分类,如果逻辑标识=0则为0,否则通过TB0011信用评级映射表匹配                                                                                                                                                                                                                                                                                                       |

**持仓信息字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| holding_quantity           | decimal | 18,2  | 是   | 否    | 0   | 持仓数量,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                              |
| holding_face_value         | decimal | 18,2  | 是   | 否    | 0   | 持仓面值,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                              |
| cost                       | decimal | 18,2  | 是   | 否    | 0   | 成本金额,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                              |
| net_cost                   | decimal | 18,2  | 是   | 否    | 0   | 净价成本,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                              |
| net_market_value           | decimal | 18,2  | 是   | 否    | 0   | 净价市值,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                              |
| market_value               | decimal | 18,2  | 是   | 否    | 0   | 市值,通过TB0001组合持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                                 |
| var_1_year                 | decimal | 18,2  | 是   | 否    | 0   | 1年风险价值,通过TB0003VaR值分析表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                           |
| var_3_year                 | decimal | 18,2  | 是   | 否    | 0   | 3年风险价值,通过TB0003VaR值分析表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                           |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额,等于市值                                                                                                                                                                                                                                                                                                                                        |
| asset_impairment_provision | decimal | 18,2  | 是   | 否    | 0   | 资产减值准备,赋值为0                                                                                                                                                                                                                                                                                                                                      |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值,等于账面余额-资产减值准备                                                                                                                                                                                                                                                                                                                             |

**期限和利率信息字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| coupon_rate                | decimal | 10,6  | 是   | 否    | 0   | 票面利率,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                            |
| annual_payment_frequency   | int     | 5     | 是   | 否    | 0   | 年付息次数,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                          |
| payment_method             | varchar | 20    | 是   | 否    | 无   | 付息方式,通过TB0013付息方式映射表匹配,匹配条件：年付息次数                                                                                                                                                                                                                                                                                                         |
| value_date                 | date    | 10    | 是   | 否    | 无   | 起息日期,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                            |
| purchase_date              | date    | 10    | 是   | 否    | 无   | 买入日期,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                            |
| maturity_date              | date    | 10    | 是   | 否    | 无   | 到期日期,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                            |
| accounting_type            | varchar | 20    | 是   | 否    | 无   | 会计分类类型,通过TB0002三账户持仓表匹配,匹配条件：证券代码                                                                                                                                                                                                                                                                                                         |

**剩余期限计算字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| remaining_term             | decimal | 10,2  | 是   | 否    | 0   | 剩余期限（年）,计算公式：ROUND((到期日-eomonth(账期转换日期,0))/365,2),账期202406转换为2024/06/30                                                                                                                                                                                                                                                                    |
| remaining_term_category    | int     | 5     | 是   | 否    | 0   | 剩余期限分类标识,剩余期限>=0为0,剩余期限<0为1                                                                                                                                                                                                                                                                                                                   |
| remaining_term_flag        | varchar | 50    | 是   | 否    | 无   | 剩余期限标识描述,根据资产类型和剩余期限区间计算：无明确期限、1年及以内、1-3年等                                                                                                                                                                                                                                                                                              |

**业务分类字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| industry_statistics_flag   | varchar | 20    | 是   | 否    | 无   | 行业统计标识,先通过TB0008匹配,如为wind中获取则通过TB0004匹配,否则通过TB0006所属行业匹配                                                                                                                                                                                                                                                                                  |
| bond_type                  | varchar | 20    | 是   | 否    | 无   | 债券类型,政府债券根据资产名称关键字判断,公司债企业债/中期票据根据行业统计标识判断                                                                                                                                                                                                                                                                                            |
| fixed_income_term_category | varchar | 50    | 是   | 否    | 无   | 固收资产剩余期限分类,通过TB0015匹配,公司债企业债/中期票据需匹配债券类型                                                                                                                                                                                                                                                                                                 |
| alm_asset_name             | varchar | 100   | 是   | 否    | 无   | ALM资产名称,先从TB0006匹配,否则从资产名称中提取(MID函数)                                                                                                                                                                                                                                                                                                         |
| bank_classification        | varchar | 50    | 是   | 否    | 无   | 银行分类,通过TB0012银行分类映射表匹配,匹配条件：ALM资产名称                                                                                                                                                                                                                                                                                                        |
| five_level_classification  | varchar | 20    | 是   | 否    | 无   | 五级分类,直接从TB0006资产定义表的五级分类字段获取,如果为空则默认为正常类                                                                                                                                                                                                                                                                                                     |

**标识字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| calculable_cashflow_flag   | varchar | 5     | 是   | 否    | 无   | 可计算现金流标识,如剩余期限<0则为0,否则通过TB0008匹配,匹配条件：资产小小类                                                                                                                                                                                                                                                                                            |
| spread_duration_statistics_flag | varchar | 5     | 是   | 否    | 无   | 利差久期资产统计标识,通过TB0008资产基础配置表匹配,匹配条件：资产小小类                                                                                                                                                                                                                                                                                                |
| single_asset_statistics_flag | varchar | 20    | 是   | 否    | 无   | 单一资产统计标识,通过TB0008资产基础配置表匹配,匹配条件：资产小小类                                                                                                                                                                                                                                                                                                   |
| five_level_statistics_flag | varchar | 20    | 是   | 否    | 无   | 五级分类资产统计标识,通过TB0008资产基础配置表匹配,匹配条件：资产小小类                                                                                                                                                                                                                                                                                                |

**流动性和折现相关字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| asset_liquidity_category   | varchar | 20    | 是   | 否    | 无   | 资产流动性分类,政府债券按债券类型+会计分类匹配,中期票据/公司债企业债按信用评级分类匹配,其他按资产小小类匹配                                                                                                                                                                                                                                                                        |
| realization_coefficient    | decimal | 10,2  | 是   | 否    | 0   | 变现系数,政府债券按债券类型+会计分类匹配,中期票据/公司债企业债非AAA为60%,AAA按多维度匹配,其他按资产小小类匹配                                                                                                                                                                                                                                                                  |
| discount_curve_rating      | varchar | 10    | 是   | 否    | 无   | 折现曲线使用评级,通过TB0011信用评级映射表匹配,匹配条件：信用评级                                                                                                                                                                                                                                                                                                     |
| discount_curve_flag        | varchar | 5     | 是   | 否    | 无   | 折现曲线标识,如可计算现金流标识=0则不涉及,否则通过TB0014匹配,存款/政府债券/中期票据/公司债企业债按资产小小类匹配,其他按资产小小类+折现曲线使用评级匹配                                                                                                                                                                                                                                        |

**调整日期字段：**

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| adjusted_value_date        | date    | 10    | 是   | 否    | 无   | 调整后起息日,计算公式：eomonth(起息日,0)                                                                                                                                                                                                                                                                                                                     |
| adjusted_purchase_date     | date    | 10    | 是   | 否    | 无   | 调整后买入日,计算公式：eomonth(买入日,0)                                                                                                                                                                                                                                                                                                                     |
| adjusted_maturity_date     | date    | 10    | 是   | 否    | 无   | 调整后到期日,计算公式：eomonth(到期日,0)                                                                                                                                                                                                                                                                                                                     |

### 1.4 用例列表

*列出所有用例场景，用例可理解为用户为完成某个作业目标而执行的一系列操作的集合，包括页面跳转、接口调用。对于批处理场景，是一系列加工处理步骤的集合*

| 用例编号   | 用例名称         | 用例描述 | 模块编号   |
| ------ | ------------ | ---- | ------ |
| UC0001 | 资产定义表维护 | 维护资产基础定义信息，包括资产名称、证券代码、资产小小类、境内外标识、信用评级、所属行业、ALM资产名称、五级分类等基础属性 | MD0001 |
| UC0002 | 账户名称映射表维护 | 维护原始账户名称到标准账户名称的映射关系，支持多种原始账户名称格式转换为统一的标准格式 | MD0001 |
| UC0003 | 资产基础配置表维护 | 维护资产小小类的各种配置标识，包括固收资产细分类、可计算现金流标识、信用评级取值逻辑标识、行业统计标识、单一资产统计标识、五级分类资产统计标识、利差久期资产统计标识等 | MD0001 |
| UC0004 | 资产配置状况分类表维护 | 维护资产配置状况的层级分类结构，支持资产大类、资产一级分类、资产二级分类、资产三级分类的层级管理，以及资产小小类到分类的映射关系 | MD0001 |
| UC0005 | 资产流动性分类及变现系数表维护 | 维护不同资产类型在不同条件下的流动性分类和变现系数，支持按资产小小类、债券类型、会计分类、信用评级等多维度配置 | MD0001 |
| UC0006 | 信用评级映射表维护 | 维护信用评级的映射转换关系，包括信用评级表使用的评级和折现曲线使用评级的映射配置 | MD0001 |
| UC0007 | 银行分类映射表维护 | 维护ALM资产名称到银行分类的映射关系，支持国有商业银行、股份制商业银行等分类 | MD0001 |
| UC0008 | 付息方式映射表维护 | 维护年付息次数到付息方式描述的映射关系，支持到期支付、按年支付、按半年支付、按季支付等方式 | MD0001 |
| UC0009 | 折现曲线配置表维护 | 维护不同资产类型和信用评级下的折现曲线配置，包括折现曲线标识和相关备注信息 | MD0001 |
| UC0010 | 固收资产剩余期限资产分类表维护 | 维护固定收益类资产的剩余期限分类配置，支持按资产小小类、境内外、债券类型等维度进行分类 | MD0001 |
| UC0011 | Wind行业表维护 | 维护Wind系统中的行业分类信息，包括证券代码、证券名称、行业分类等数据的导入和维护 | MD0001 |
| UC0012 | Wind评级表维护 | 维护Wind系统中的评级信息，包括证券代码、证券简称、主体评级、债项评级等数据的导入和维护 | MD0001 |
| UC0013 | 整体资产明细表生成 | 基于资产定义表和各种映射配置表，生成完整的资产明细信息，包括资产分类、评级信息、持仓信息、期限信息等综合数据 | MD0001 |
| UC0014 | 资产宽表数据导出 | 支持将整体资产明细表数据按不同维度导出，包括Excel导出、CSV导出等格式，满足监管报告和业务分析需求 | MD0001 |
| UC0015 | 资产数据质量检查 | 对资产宽表数据进行质量检查，包括数据完整性检查、业务逻辑校验、数据一致性验证等 | MD0001 |

## 2. 业务概念与术语

*解释专业术语，可提升AI对专业术语的理解，更好地生成代码*

| 术语  | 释义                                                                     |
| --- | ---------------------------------------------------------------------- |
| 资产宽表 | 将分散在多个系统和表中的资产相关数据整合到一个综合性表中，提供完整的资产视图，便于分析和报告 |

## 3. 功能需求

### 3.1 资产宽表模块

#### 3.1.1 页面描述

#### 3.1.1.1 页面列表

| 页面编号   | 页面名称         | 类型  | 依赖的主页面编号 |
| ------ | ------------ | --- | -------- |
| PG0001 | 资产定义表维护页     | 主页面 |          |
| PG0002 | 资产定义表详情页     | 弹层  | PG0001   |
| PG0003 | 账户名称映射表维护页   | 主页面 |          |
| PG0004 | 资产基础配置表维护页   | 主页面 |          |
| PG0005 | 资产配置状况分类表维护页 | 主页面 |          |
| PG0006 | 资产流动性配置表维护页  | 主页面 |          |
| PG0007 | 信用评级映射表维护页   | 主页面 |          |
| PG0008 | 银行分类映射表维护页   | 主页面 |          |
| PG0009 | 付息方式映射表维护页   | 主页面 |          |
| PG0010 | 折现曲线配置表维护页   | 主页面 |          |
| PG0011 | 固收资产期限分类表维护页 | 主页面 |          |
| PG0012 | Wind行业表维护页    | 主页面 |          |
| PG0013 | Wind评级表维护页    | 主页面 |          |
| PG0014 | 整体资产明细表查询页   | 主页面 |          |
| PG0015 | 整体资产明细表详情页   | 弹层  | PG0014   |
| PG0016 | 资产数据质量检查页    | 主页面 |          |

#### ******* 整体资产明细表查询页(PG0014)

##### *******.1 原型图

*整体资产明细表查询页面原型图*

##### *******.2 输入区

**查询条件：**

| 字段名称   | 字段类型   | 是否必填 | 默认值 | 说明                                    |
| ------ | ------ | ---- | --- | ------------------------------------- |
| 账期     | 下拉选择   | 是    | 当前月 | 格式YYYYMM，如202406                     |
| 账户名称   | 下拉选择   | 否    | 全部  | 传统账户、分红账户、万能账户、独立账户、资本补充债账户、普通账户 |
| 资产小小类  | 下拉选择   | 否    | 全部  | 存款、政府债券、中期票据等                       |
| 资产大类   | 下拉选择   | 否    | 全部  | 现金及流动性管理工具、固定收益类投资资产、权益类投资资产等     |
| 境内外    | 下拉选择   | 否    | 全部  | 境内资产、境外资产                           |
| 信用评级   | 下拉选择   | 否    | 全部  | AAA、AA+、AA、无评级等                     |
| 资产名称   | 文本输入   | 否    | 无   | 支持模糊查询                              |
| 证券代码   | 文本输入   | 否    | 无   | 支持模糊查询                              |

**操作按钮：**
- 查询：执行查询操作
- 重置：清空查询条件
- 导出：导出查询结果到Excel
- 数据生成：触发整体资产明细表数据生成

##### *******.3 展示区

**列表展示字段：**

| 字段名称     | 字段类型   | 宽度  | 说明                    |
| -------- | ------ | --- | --------------------- |
| 账期       | 文本     | 80  | YYYYMM格式              |
| 资产编号     | 文本     | 120 | 资产唯一标识                |
| 账户名称     | 文本     | 100 | 账户类型                  |
| 资产名称     | 文本     | 200 | 资产具体名称，支持tooltip显示完整内容 |
| 证券代码     | 文本     | 100 | 证券标识代码                |
| 资产小小类    | 文本     | 120 | 资产最细分类                |
| 资产大类     | 文本     | 150 | 资产主要分类                |
| 境内外      | 文本     | 80  | 境内外标识                 |
| 信用评级     | 文本     | 80  | 最终信用评级                |
| 市值       | 数值     | 120 | 格式：千分位分隔，保留2位小数       |
| 剩余期限     | 数值     | 100 | 单位：年，保留2位小数           |
| 剩余期限标识   | 文本     | 120 | 期限区间描述                |
| 变现系数     | 百分比    | 100 | 保留2位小数                |
| 资产流动性分类  | 文本     | 120 | 流动性分类                 |
| 操作       | 操作按钮组  | 100 | 查看详情                  |

**分页设置：**
- 默认每页显示20条记录
- 支持每页10/20/50/100条记录选择
- 显示总记录数和页码信息

**排序功能：**
- 支持按市值、剩余期限等数值字段排序
- 默认按资产编号升序排列

##### *******.4 其他描述

**页面特性：**
- 支持大数据量查询，采用分页加载方式
- 查询结果支持Excel导出，包含完整字段信息
- 数据展示支持千分位分隔符和小数位控制
- 支持查询条件的保存和恢复
- 提供数据刷新功能，确保数据实时性

**权限控制：**
- 查询权限：所有用户可查询
- 导出权限：需要导出权限
- 数据生成权限：需要管理员权限

**性能要求：**
- 查询响应时间不超过3秒
- 导出功能支持最大10万条记录
- 页面加载时间不超过2秒

#### 3.1.2 接口功能

##### 3.1.2.1 整体资产明细表查询接口

##### 3.1.2.1.1 接口功能概述

提供整体资产明细表的分页查询功能，支持多维度条件筛选，返回符合条件的资产明细数据。

##### 3.1.2.1.2 接口基本信息

| 属性   | 值                                    |
| ---- | ------------------------------------ |
| 接口名称 | 整体资产明细表查询                            |
| 请求方式 | POST                                 |
| 接口地址 | /api/asset/detail/query              |
| 接口描述 | 根据查询条件分页查询整体资产明细表数据                  |
| 开发者  | 待分配                                  |

##### 3.1.2.1.3 接口入参

| 参数名       | 类型      | 是否必填 | 描述   |
| --------- | ------- | ---- | ---- |
| accountingPeriod | String | 是 | 账期，格式YYYYMM |
| accountName | String | 否 | 账户名称 |
| assetSubSubCategory | String | 否 | 资产小小类 |
| assetMajorCategory | String | 否 | 资产大类 |
| domesticForeign | String | 否 | 境内外标识 |
| creditRating | String | 否 | 信用评级 |
| assetName | String | 否 | 资产名称，支持模糊查询 |
| securityCode | String | 否 | 证券代码，支持模糊查询 |
| pageNum | Integer | 是 | 页码，从1开始 |
| pageSize | Integer | 是 | 每页记录数，默认20 |
| orderBy | String | 否 | 排序字段 |
| orderDirection | String | 否 | 排序方向，ASC/DESC |

**请求示例：**
```json
{
  "accountingPeriod": "202406",
  "accountName": "传统账户",
  "assetSubSubCategory": "政府债券",
  "pageNum": 1,
  "pageSize": 20,
  "orderBy": "marketValue",
  "orderDirection": "DESC"
}
```

##### 3.1.2.1.4 接口出参

| 参数名       | 类型      | 描述   |
| --------- | ------- | ---- |
| code | Integer | 响应码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.total | Long | 总记录数 |
| data.pages | Integer | 总页数 |
| data.pageNum | Integer | 当前页码 |
| data.pageSize | Integer | 每页记录数 |
| data.list | Array | 资产明细列表 |
| data.list[].accountingPeriod | String | 账期 |
| data.list[].assetNumber | String | 资产编号 |
| data.list[].accountName | String | 账户名称 |
| data.list[].assetName | String | 资产名称 |
| data.list[].securityCode | String | 证券代码 |
| data.list[].assetSubSubCategory | String | 资产小小类 |
| data.list[].assetMajorCategory | String | 资产大类 |
| data.list[].domesticForeign | String | 境内外标识 |
| data.list[].creditRating | String | 信用评级 |
| data.list[].marketValue | BigDecimal | 市值 |
| data.list[].remainingTerm | BigDecimal | 剩余期限 |
| data.list[].remainingTermFlag | String | 剩余期限标识 |
| data.list[].realizationCoefficient | BigDecimal | 变现系数 |
| data.list[].assetLiquidityCategory | String | 资产流动性分类 |

**响应示例：**

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 1000,
    "pages": 50,
    "pageNum": 1,
    "pageSize": 20,
    "list": [
      {
        "accountingPeriod": "202406",
        "assetNumber": "AST001",
        "accountName": "传统账户",
        "assetName": "国债2024001",
        "securityCode": "019666",
        "assetSubSubCategory": "政府债券",
        "assetMajorCategory": "固定收益类投资资产",
        "domesticForeign": "境内资产",
        "creditRating": "AAA",
        "marketValue": ********.00,
        "remainingTerm": 2.5,
        "remainingTermFlag": "1-3年（含3年）",
        "realizationCoefficient": 0.95,
        "assetLiquidityCategory": "高流动性资产"
      }
    ]
  }
}
```

| 参数名       | 类型      | 是否必填 | 描述   |
| --------- | ------- | ---- | ---- |
| name      | string  | 是    | 名称   |
| users     | array   | 是    | 用户数组 |
| [].id     | number  | 是    | 用户ID |
| [].name   | string  | 是    | 用户名  |
| user      | object  | 是    | 用户对象 |
| user.id   | integer | 是    | 用户ID |
| user.name | string  | 是    | 用户名  |

**【示例】：**

```json
{
    "name": "小明",
    "users": [{
            "id": 11111,
            "name": "小武"
        },
        {
            "id": 22222,
            "name": "小刘"
        }
    ],
    "user": {"id": 33333, "name": "小强"}
}
```

##### 3.1.2.1.4 接口出参

| 参数名  | 类型      | 描述             |
| ---- | ------- | -------------- |
| code | integer | 代码为200为成功,反则异常 |
| msg  | string  | 异常描述           |

**【成功示例】:**

```json
{
    "code": 200,
    "msg": ""
}
```

**【失败示例】:**

```json
{
    "code": 500,
    "msg": "系统异常,请联系管理员!"
}
```

##### 3.1.2.1.5 接口功能详述

**实现步骤：**

1. **参数校验**
   - 校验必填参数accountingPeriod格式是否正确
   - 校验分页参数pageNum和pageSize的有效性
   - 校验排序参数的合法性

2. **构建查询条件**
   - 根据传入的查询参数构建WHERE条件
   - 处理模糊查询字段（资产名称、证券代码）
   - 构建排序条件

3. **执行查询**
   - 查询总记录数用于分页计算
   - 执行分页查询获取当前页数据
   - 对数值字段进行格式化处理

4. **数据转换**
   - 将数据库字段转换为前端展示格式
   - 处理字典值的转换（如账户名称、资产分类等）
   - 格式化数值字段（千分位分隔符、小数位数）

5. **返回结果**
   - 构建分页响应对象
   - 返回查询结果和分页信息

**异常处理：**
- 参数校验失败返回400错误
- 数据库查询异常返回500错误
- 无数据时返回空列表但保持正常响应结构

#### 3.1.3 用例描述

##### 3.1.3.1 UC0013 - 整体资产明细表生成

**用例描述：**
基于资产定义表和各种映射配置表，生成完整的资产明细信息，包括资产分类、评级信息、持仓信息、期限信息等综合数据。

**前置条件：**
- 资产定义表(TB0006)已维护基础资产信息
- 各种映射表和配置表已完成配置
- 外部系统数据（持仓、评级等）已同步

**主要流程：**
1. 读取资产定义表作为基础数据源
2. 通过账户名称映射表转换账户名称
3. 通过资产基础配置表获取各种标识和配置
4. 通过资产配置状况分类表获取分类信息
5. 关联外部系统获取持仓、市值、评级等数据
6. 执行复杂的业务逻辑计算（信用评级、剩余期限、流动性系数等）
7. 生成完整的整体资产明细表数据

**业务规则：**
- 信用评级按照6种逻辑标识进行取值
- 剩余期限根据到期日和账期进行计算
- 资产分类按照层级结构进行匹配
- 流动性系数根据资产类型和评级进行计算

**后置条件：**
- 整体资产明细表数据生成完成
- 数据质量检查通过
- 可供查询和导出使用

**详细字段生成逻辑：**

*本部分按照TB0016整体资产明细表的56个字段逐一说明生成规则*

**步骤1. 基础信息字段生成**

(1) **accounting_period**（账期）：输入参数账期

(2) **asset_number**（资产编号）：每个账期从1开始编号，使用ROW_NUMBER()函数按id排序生成

(3) **account_name**（账户名称）：
```sql
SELECT
  CASE
    WHEN t7.account_name_mapping IS NOT NULL THEN
      (SELECT dict_label FROM sys_dict_data WHERE dict_type='ast_account_name_mapping' AND dict_value=t7.account_name_mapping)
    ELSE TB0006.account_name
  END as account_name
FROM TB0007 t7
WHERE t7.account_name = TB0006.account_name
  AND t7.accounting_period = [当前账期]
```
匹配条件：资产定义表.账户名称=账户名称映射表.账户名称 AND 账期匹配，获取映射后的账户名称字典标签，如果没有映射则使用原始账户名称
**注意**：账户名称在存入表时应该为字典数据，以便在页面按照账户名称搜索条件查询时能正确匹配

(4) **security_code**（证券代码）：TB0006.security_code

(5) **asset_name**（资产名称）：TB0006.asset_name

(6) **asset_sub_sub_category**（资产小小类）：TB0006.asset_sub_sub_category

(7) **asset_major_category**（资产大类）：
```sql
SELECT category_name FROM TB0009
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
  AND (domestic_foreign = TB0006.domestic_foreign OR domestic_foreign IS NULL)
  AND category_level = 1
  AND accounting_period = [当前账期]
```
匹配条件：资产定义表.资产小小类 = 资产配置状况分类表.资产小小类 AND 境内外标识匹配 AND 账期匹配
**注意**：融资回购（asset_sub_sub_category='22'）需要特殊处理，确保能找到对应的资产大类

(8) **domestic_foreign**（境内外）：TB0006.domestic_foreign

**步骤2. 剩余期限计算（先计算，供后续步骤使用）**

(9) **remaining_term**（剩余期限）：
计算公式：ROUND((maturity_date - LAST_DAY(STR_TO_DATE(CONCAT(accounting_period, '01'), '%Y%m%d')))/365, 2)
说明：从到期日到账期月底的天数差，再换算为年数（除以365），并保留两位小数
示例：账期202406转化为2024/06/30，计算到期日与2024/06/30的差值

(10) **remaining_term_category**（剩余期限分类）：
- 如果remaining_term IS NULL：remaining_term_category = 0
- 如果remaining_term >= 0：remaining_term_category = 0
- 如果remaining_term < 0：remaining_term_category = 1

(11) **remaining_term_flag**（剩余期限标识）：
- 如果remaining_term IS NULL：remaining_term_flag = NULL
- 如果asset_sub_sub_category IN ('固定收益类保险资产管理产品', '货币类保险资产管理产品', '债券型基金') OR remaining_term < 0：remaining_term_flag = '无明确期限'
- 如果remaining_term <= 1：remaining_term_flag = '1年及以内'
- 如果1 < remaining_term <= 3：remaining_term_flag = '1-3年（含3年）'
- 如果3 < remaining_term <= 5：remaining_term_flag = '3-5年（含5年）'
- 如果5 < remaining_term <= 7：remaining_term_flag = '5-7年（含7年）'
- 如果7 < remaining_term <= 10：remaining_term_flag = '7-10年（含10年）'
- 如果10 < remaining_term <= 15：remaining_term_flag = '10-15年（含15年）'
- 如果remaining_term > 15：remaining_term_flag = '15年以上'

**步骤3. 分类信息字段生成**

(12) **fixed_income_sub_category**（固收资产细分类）：
```sql
SELECT
  CASE
    WHEN asset_major_category = '固定收益类投资资产' THEN
      (SELECT fixed_income_sub_category FROM TB0008
       WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
         AND accounting_period = [当前账期])
    ELSE NULL
  END as fixed_income_sub_category
```
匹配条件：只有当资产大类为"固定收益类投资资产"时，才从资产基础配置表获取固收资产细分类

(13) **asset_allocation_level1**（资产配置状况一级分类）：
```sql
SELECT category_name FROM TB0009
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
  AND (
    CASE
      WHEN TB0006.asset_sub_sub_category = '22' THEN 1=1  -- 融资回购不考虑境内外标识
      ELSE
        (domestic_foreign = TB0006.domestic_foreign OR domestic_foreign IS NULL)
    END
  )
  AND category_level = 2
  AND accounting_period = [当前账期]
```
匹配条件：直接使用资产小小类进行匹配，如果资产小小类为融资回购（'22'），则不考虑境内外标识；其他情况按境内外标识匹配

(14) **asset_allocation_level2**（资产配置状况二级分类）：
```sql
SELECT
  CASE
    WHEN TB0006.asset_sub_sub_category = '02' AND remaining_term > 1 THEN NULL  -- 政府债券且剩余期限>1年
    WHEN TB0006.asset_sub_sub_category = '02' AND remaining_term <= 1 THEN (
      SELECT category_name FROM TB0009
      WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
        AND (domestic_foreign = TB0006.domestic_foreign OR domestic_foreign IS NULL)
        AND category_level = 3
        AND accounting_period = [当前账期]
    )
    WHEN TB0006.asset_sub_sub_category <> '02' THEN (
      SELECT category_name FROM TB0009
      WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
        AND (domestic_foreign = TB0006.domestic_foreign OR domestic_foreign IS NULL)
        AND category_level = 3
        AND accounting_period = [当前账期]
    )
    ELSE NULL
  END as asset_allocation_level2
```
匹配条件：
- 如果资产小小类=政府债券（'02'）AND 剩余期限>1：返回NULL
- 如果资产小小类=政府债券（'02'）AND 剩余期限<=1：从TB0009获取三级分类
- 如果资产小小类<>政府债券：从TB0009获取三级分类

(15) **asset_allocation_level3**（资产配置状况三级分类）：
```
SELECT category_name FROM TB0009
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
AND domestic_foreign = TB0006.domestic_foreign
AND category_level = 4
```

**步骤4. 评级信息字段生成**

(14) **wind_entity_rating**（Wind中主体评级）：
```
SELECT entity_rating
FROM TB0005 Wind评级表
WHERE TB0006.security_code = TB0005.security_code
```
匹配条件：资产定义表.证券代码 = Wind评级表.证券代码，匹配主体评级

(15) **wind_debt_rating**（Wind中债项评级）：
```
SELECT bond_rating
FROM TB0005 Wind评级表
WHERE TB0006.security_code = TB0005.security_code
```
匹配条件：资产定义表.证券代码 = Wind评级表.证券代码，匹配债项评级

(16) **risk_entity_rating**（风控绩效系统中主体评级）：
```
SELECT 主体评级
FROM TB0002 三账户持仓表
WHERE TB0006.security_code = TB0002.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配主体评级

(17) **risk_debt_rating**（风控绩效系统中债项评级）：
```
SELECT 债项评级
FROM TB0002 三账户持仓表
WHERE TB0006.security_code = TB0002.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配债项评级

(18) **credit_rating_maintenance**（信用评级维护表中信用评级）：
直接从TB0006资产定义表获取：TB0006.credit_rating
匹配条件：资产定义表.证券代码，匹配信用评级字段

(19) **credit_rating_logic_flag**（信用评级取值逻辑标识）：
```
SELECT credit_rating_logic_flag
FROM TB0008 资产基础配置表
WHERE TB0006.asset_sub_sub_category = TB0008.asset_sub_sub_category
```
匹配条件：资产定义表.资产小小类=资产基础配置表.资产小小类，匹配信用评级取值逻辑标识

(20) **credit_rating**（信用评级）：
根据信用评级取值逻辑标识计算：
- 如果信用评级取值逻辑标识=0：credit_rating = "不涉及"
- 如果信用评级取值逻辑标识=1：credit_rating = "无评级"
- 如果信用评级取值逻辑标识=2：credit_rating = "免评级"
- 如果信用评级取值逻辑标识=3：
  - 如果风控绩效系统中债项评级<>空：credit_rating = risk_debt_rating（风控绩效系统中债项评级）
  - 如果风控绩效系统中债项评级=空：credit_rating = risk_entity_rating（风控绩效系统中主体评级）
- 如果信用评级取值逻辑标识=4：
  - 如果Wind中债项评级<>空：credit_rating = wind_debt_rating（Wind中债项评级）
  - 如果Wind中债项评级=空：credit_rating = wind_entity_rating（Wind中主体评级）
- 如果信用评级取值逻辑标识=5：credit_rating = credit_rating_maintenance（信用评级维护表中信用评级）

注：直接使用前面步骤中已获取的wind_entity_rating、wind_debt_rating、risk_entity_rating、risk_debt_rating字段值，无需再次查询其他表。

(21) **credit_rating_category**（信用评级分类）：
```sql
SELECT
  CASE
    WHEN t8.credit_rating_logic_flag = '0' THEN '不涉及'
    ELSE
      CASE
        WHEN t11.credit_rating_table_used IS NOT NULL THEN
          (SELECT dict_label FROM sys_dict_data WHERE dict_type='ast_credit_rating' AND dict_value=t11.credit_rating_table_used)
        ELSE '无评级'
      END
  END as credit_rating_category
FROM TB0011 t11, TB0008 t8
WHERE t11.credit_rating = [步骤20计算的credit_rating值]
  AND t11.accounting_period = [当前账期]
  AND t8.asset_sub_sub_category = TB0006.asset_sub_sub_category
  AND t8.accounting_period = [当前账期]
```
匹配条件：
- 如果信用评级取值逻辑标识=0：返回"不涉及"
- 否则：使用步骤(20)中计算得出的credit_rating值匹配TB0011表，获取credit_rating_table_used字段对应的字典标签

**步骤4. 持仓信息字段生成**

(21) **holding_quantity**（持仓数量）：
```
SELECT 持仓数量 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配持仓数量

(22) **holding_face_value**（持仓面值）：
```
SELECT 持仓面值 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配持仓面值

(23) **cost**（成本）：
```
SELECT 成本 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配成本

(24) **net_cost**（净价成本）：
```
SELECT 净价成本 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配净价成本

(25) **net_market_value**（净价市值）：
```
SELECT 净价市值 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配净价市值

(26) **market_value**（市值）：
```sql
SELECT market_value FROM TB0001
WHERE security_code = TB0006.security_code
  AND account_name = TB0006.account_name
  AND accounting_period = [当前账期]
```
匹配条件：资产定义表.证券代码 = 组合持仓表.证券代码 AND 资产定义表.账户名称 = 组合持仓表.账户名称 AND 账期匹配，获取市值

(27) **var_1_year**（1年VAR值）：
```sql
SELECT var_amount FROM TB0003
WHERE security_code = TB0006.security_code
  AND data_type IN ('债基1年', '权益1年')
  AND accounting_period = [当前账期]
```
匹配条件：资产定义表.证券代码 = VaR值分析表.证券代码 AND 数据类型为债基1年或权益1年 AND 账期匹配，获取VaR金额

(28) **var_3_year**（3年VAR值）：
```sql
SELECT var_amount FROM TB0003
WHERE security_code = TB0006.security_code
  AND data_type IN ('债基3年', '权益3年')
  AND accounting_period = [当前账期]
```
匹配条件：资产定义表.证券代码 = VaR值分析表.证券代码 AND 数据类型为债基3年或权益3年 AND 账期匹配，获取VaR金额

(29) **coupon_rate**（票面利率）：
```
SELECT 票面利率 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配票面利率

(30) **annual_payment_frequency**（年付息次数）：
```
SELECT 年付息次数 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配年付息次数

(31) **payment_method**（付息方式）：
```sql
SELECT payment_method FROM TB0013
WHERE annual_payment_frequency = [步骤30计算的annual_payment_frequency值]
  AND accounting_period = [当前账期]
```
匹配条件：使用步骤(30)中获取的年付息次数值=付息方式映射表.年付息次数 AND 账期匹配

(32) **value_date**（起息日）：
```
SELECT 起息日 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配起息日

(33) **purchase_date**（买入日期）：
```
SELECT 买入日期 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配买入日期

(34) **maturity_date**（到期日）：
```
SELECT 到期日 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配到期日

(35) **accounting_type**（会计类型）：
```
SELECT 会计类型 FROM TB0002
WHERE 证券代码 = TB0006.证券代码
```
匹配条件：资产定义表.证券代码 = 三账户持仓表.证券代码，匹配会计类型

(36) **book_balance**（账面余额）：
- 如果market_value不为空：book_balance = market_value
- 如果market_value为空：book_balance = net_market_value（净价市值）

(37) **asset_impairment_provision**（资产减值准备）：赋值为0

(38) **book_value**（账面价值）：等于book_balance - asset_impairment_provision

**步骤5. 业务分类字段生成**

(39) **industry_statistics_flag**（行业统计标识）：
首先匹配资产基础配置表，根据匹配结果进行处理：
```sql
SELECT industry_statistics_flag FROM TB0008
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
  AND accounting_period = [当前账期]
```
匹配结果分以下情况处理：
- 匹配结果="不考虑"：industry_statistics_flag = "不考虑"
- 匹配结果="银行"：industry_statistics_flag = "银行"
- 匹配结果="非银金融"：industry_statistics_flag = "非银金融"
- 匹配结果="房地产"：industry_statistics_flag = "房地产"
- 匹配结果="建筑装饰"：industry_statistics_flag = "建筑装饰"
- 匹配结果="wind中获取"：
  ```sql
  CASE
    WHEN wind_industry.industry IS NOT NULL AND wind_industry.industry != '' THEN wind_industry.industry
    WHEN TB0006.industry_category IS NOT NULL AND TB0006.industry_category != '' THEN TB0006.industry_category
    ELSE '需要维护行业信息'
  END
  ```
  执行逻辑：
  1. 优先从TB0004 Wind行业表获取行业信息（根据证券代码匹配）
  2. 如果Wind行业表中没有数据，则使用资产定义表中的所属行业分类
  3. 如果资产定义表中也没有数据，则显示"需要维护行业信息"

(43) **bond_type**（债券类型）：
```sql
CASE
  WHEN asset_sub_sub_category = '政府债券' THEN
    CASE
      WHEN asset_name LIKE '%国债%' THEN '国债'
      WHEN asset_name LIKE '%国开%' OR asset_name LIKE '%进出口%' OR asset_name LIKE '%农发%' THEN '政策性银行金融债'
      WHEN asset_name LIKE '%铁道%' THEN '准政府债'
      ELSE '政府债'
    END
  WHEN asset_sub_sub_category IN ('公司债企业债', '中期票据') THEN
    CASE
      WHEN industry_statistics_flag IN ('银行', '非银金融') THEN '金融企业债'
      ELSE '非金融企业债'
    END
  ELSE NULL
END
```

(44) **fixed_income_term_category**（固收资产剩余期限资产分类）：
- 如果资产小小类=公司债企业债 OR 中期票据：
  ```sql
  SELECT fixed_income_term_category FROM TB0015
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
    AND domestic_foreign = TB0006.domestic_foreign
    AND bond_type = [步骤43计算的债券类型]
    AND account_period = [当前账期]
  ```
  匹配条件：资产定义表.资产小小类=固收资产剩余期限资产分类表.资产小小类 AND 资产定义表.境内外=固收资产剩余期限资产分类表.境内外 AND 资产定义表.债券类型=固收资产剩余期限资产分类表.债券类型 AND 账期匹配

- 如果资产小小类<>公司债企业债 AND 资产小小类<>中期票据：
  ```sql
  SELECT fixed_income_term_category FROM TB0015
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
    AND domestic_foreign = TB0006.domestic_foreign
    AND account_period = [当前账期]
  ```
  匹配条件：资产定义表.资产小小类=固收资产剩余期限资产分类表.资产小小类 AND 资产定义表.境内外=固收资产剩余期限资产分类表.境内外 AND 账期匹配

(45) **calculable_cashflow_flag**（可计算现金流固收资产标识）：
- 如果剩余期限<0：可计算现金流固收资产标识=0
- 如果剩余期限>=0：
  ```
  SELECT 可计算现金流固收资产标识 FROM TB0008
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
  ```

(46) **spread_duration_statistics_flag**（利差久期资产统计标识）：
```
SELECT 利差久期资产统计标识 FROM TB0008
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
```

(47) **alm_asset_name**（ALM资产名称）：
- 首先从资产定义表获取：TB0006.alm_asset_name
- 如果为空，则使用MID函数：MID(TB0006.asset_name,FIND("-",TB0006.asset_name,1)+2,100)
- 如出现新增不规则特殊资产，需提示用户维护资产名称映射表

(48) **single_asset_statistics_flag**（单一资产统计标识）：
```
SELECT 单一资产统计标识 FROM TB0008
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
```

(49) **five_level_classification**（五级分类）：
- 首先从TB0006资产定义表根据证券代码获取：TB0006.five_level_classification
- 如果为空：five_level_classification = "正常类"

(50) **five_level_statistics_flag**（五级分类资产统计标识）：
```
SELECT 五级分类资产统计标识 FROM TB0008
WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
```

(51) **bank_classification**（银行分类）：
```sql
SELECT bank_classification FROM TB0012
WHERE alm_asset_name = [步骤47计算的ALM资产名称]
  AND accounting_period = [当前账期]
```
匹配条件：资产定义表.ALM资产名称=银行分类映射表.ALM资产名称 AND 账期匹配

**步骤7. 流动性和折现相关字段生成**

(52) **asset_liquidity_category**（资产流动性分类）：
- 资产小小类=政府债券：
  ```sql
  SELECT asset_liquidity_category FROM TB0010
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
    AND bond_type = [步骤43计算的债券类型]
    AND accounting_classification = [步骤35计算的会计类型]
    AND accounting_period = [当前账期]
  ```
  匹配条件：整体资产明细表.资产小小类=资产流动性分类及变现系数表.资产小小类 AND 整体资产明细表.债券类型=资产流动性分类及变现系数表.债券类型 AND 整体资产明细表.会计分类=资产流动性分类及变现系数表.会计分类 AND 账期匹配

- 资产小小类=中期票据 OR 公司债企业债：
  - 如果步骤(20)计算的信用评级<>AAA：资产流动性分类=中低流动性资产
  - 如果步骤(20)计算的信用评级=AAA：
    ```sql
    SELECT asset_liquidity_category FROM TB0010
    WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
      AND bond_type = [步骤43计算的债券类型]
      AND accounting_classification = [步骤35计算的会计类型]
      AND credit_rating = [步骤20计算的信用评级]
      AND accounting_period = [当前账期]
    ```
    匹配条件：整体资产明细表.资产小小类=资产流动性分类及变现系数表.资产小小类 AND 整体资产明细表.债券类型=资产流动性分类及变现系数表.债券类型 AND 整体资产明细表.会计分类=资产流动性分类及变现系数表.会计分类 AND 整体资产明细表.信用评级=资产流动性分类及变现系数表.信用评级 AND 账期匹配

- 资产小小类=其他：
  ```sql
  SELECT asset_liquidity_category FROM TB0010
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
    AND accounting_period = [当前账期]
  ```
  匹配条件：整体资产明细表.资产小小类=资产流动性分类及变现系数表.资产小小类 AND 账期匹配

(53) **realization_coefficient**（变现系数）：
- 资产小小类=政府债券：
  ```sql
  SELECT realization_coefficient FROM TB0010
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
    AND bond_type = [步骤43计算的债券类型]
    AND accounting_classification = [步骤35计算的会计类型]
    AND accounting_period = [当前账期]
  ```
  匹配条件：整体资产明细表.资产小小类=资产流动性分类及变现系数表.资产小小类 AND 整体资产明细表.债券类型=资产流动性分类及变现系数表.债券类型 AND 整体资产明细表.会计分类=资产流动性分类及变现系数表.会计分类 AND 账期匹配

- 资产小小类=中期票据 OR 公司债企业债：
  - 如果步骤(20)计算的信用评级<>AAA：变现系数=60%
  - 如果步骤(20)计算的信用评级=AAA：
    ```sql
    SELECT realization_coefficient FROM TB0010
    WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
      AND bond_type = [步骤43计算的债券类型]
      AND accounting_classification = [步骤35计算的会计类型]
      AND credit_rating = [步骤20计算的信用评级]
      AND accounting_period = [当前账期]
    ```
    匹配条件：整体资产明细表.资产小小类=资产流动性分类及变现系数表.资产小小类 AND 整体资产明细表.债券类型=资产流动性分类及变现系数表.债券类型 AND 整体资产明细表.会计分类=资产流动性分类及变现系数表.会计分类 AND 整体资产明细表.信用评级=资产流动性分类及变现系数表.信用评级 AND 账期匹配

- 资产小小类=其他：
  ```sql
  SELECT realization_coefficient FROM TB0010
  WHERE asset_sub_sub_category = TB0006.asset_sub_sub_category
    AND accounting_period = [当前账期]
  ```
  匹配条件：整体资产明细表.资产小小类=资产流动性分类及变现系数表.资产小小类 AND 账期匹配

(54) **discount_curve_rating**（折现曲线使用评级）：
```sql
SELECT discount_curve_rating FROM TB0011
WHERE credit_rating = [步骤20计算的credit_rating值]
  AND accounting_period = [当前账期]
```
匹配条件：使用步骤(20)中计算的信用评级值=信用评级映射表.信用评级 AND 账期匹配

(55) **discount_curve_flag**（折现曲线标识）：
- 如果步骤中计算的可计算现金流固收资产标识=0：折现曲线标识=不涉及
- 如果资产小小类=存款 OR 政府债券 OR 中期票据 OR 公司债企业债：
  ```sql
  SELECT discount_curve_flag FROM TB0014
  WHERE asset_sub_sub_category = [TB0006.asset_sub_sub_category]
    AND accounting_period = [当前账期]
  ```
- 如果资产小小类=其他：
  ```sql
  SELECT discount_curve_flag FROM TB0014
  WHERE asset_sub_sub_category = [TB0006.asset_sub_sub_category]
    AND discount_curve_rating = [步骤54计算的discount_curve_rating值]
    AND accounting_period = [当前账期]
  ```

**步骤8. 调整日期字段生成**

(56) **adjusted_value_date**（调整起息日）：
```sql
CASE
  WHEN value_date IS NOT NULL THEN LAST_DAY(value_date)
  ELSE NULL
END
```
说明：将起息日转换为该月的最后一天

(57) **adjusted_purchase_date**（调整买入日）：
```sql
CASE
  WHEN purchase_date IS NOT NULL THEN LAST_DAY(purchase_date)
  ELSE NULL
END
```
说明：将买入日转换为该月的最后一天

(58) **adjusted_maturity_date**（调整到期日）：
```sql
CASE
  WHEN maturity_date IS NOT NULL THEN LAST_DAY(maturity_date)
  ELSE NULL
END
```
说明：将到期日转换为该月的最后一天

**步骤9. 数据入表**
(1) 将所有计算结果插入TB0016整体资产明细表
(2) 执行数据质量检查，确保关键字段完整性
(3) 记录处理日志，包括处理记录数、异常记录数等统计信息

##### 3.1.3.2 UC0014 - 资产宽表数据导出

**用例描述：**
支持将整体资产明细表数据按不同维度导出，包括Excel导出、CSV导出等格式，满足监管报告和业务分析需求。

**主要功能：**
- 支持按查询条件导出筛选后的数据
- 支持全量数据导出
- 支持自定义导出字段
- 支持多种导出格式（Excel、CSV）
- 支持大数据量分批导出

**导出格式要求：**
- Excel格式包含表头和边框
- 数值字段保留适当小数位数
- 字典值显示中文描述
- 日期字段统一格式
- 文件名包含导出时间和条件信息

# 附录1

## 接口请求参数类型：

| 类型       | 说明                      | 示例                             |
| -------- | ----------------------- | ------------------------------ |
| string   | 文本类型，需定义长度限制            | "username": "john_doe"         |
| number   | 包括整数和浮点数                | "age": 25                      |
| boolean  | 布尔值（true/false）         | "is_active": true              |
| array    | 数组，需声明元素类型              | "tags": ["urgent", "feature"]  |
| object   | 键值对嵌套结构                 | "address": {"city": "Beijing"} |
| date     | 日期（YYYY-MM-DD）          | "2025-06-19"                   |
| datetime | 时间（YYYY-MM-DD HH:MM:SS） | "2025-06-19 14:30:00"          |
| file     | 文件上传                    | 文件二进制数据                        |

# 附录2

## 1. 功能逻辑表达方法：

### 1.1 面向对象法

**核心原则**：以数据载体为核心构建逻辑描述，确保 AI 可精准解析数据流向

- 必须明确指定数据操作对象（表 / 缓存 / 文件），避免模糊动作词汇
- 采用 "对象 + 字段 + 操作" 的三元组结构，示例：`从TB0001表查询字段A，对字段B执行聚合操作后写入TB0002表`

**【正例】：**
**步骤1.** 现金流汇总
以账期为202412作为条件查询TB0001表，以账期,现金流类型,基点类型,久期类型,设计类型,是否中短期作为汇总字段,针对cash_val_set的value值按对应序号进行加合汇总，所有数据汇总完成后写入TB0002表
**说明：**

- 这里所面向的对象是表TB0001和TB0002，AI会自动在文档中检索表名并转换为英文名
- 可以直接使用字段的中文名称进行描述，AI会自动转为字段英文名生成代码和SQL，但中文名可能作为前缀出现在文档的非字段描述位置，为了避免识别问题，建议使用英文名描述
- 不需要详细描述DTO和Entity赋值逻辑，AI会自动识别赋值并写入对应表

**【反例】：**
**步骤1.** 现金流汇总
请对账期为202412的现金流数据实现汇总
**说明：**

- 没有描述清楚数据来源
- 没有描述清楚数据加工方法
- 没有描述结果数据如何处理

### 1.2 符号引用法

* 前面的面向对象的方法中可以看到，表名使用TB0001和TB0002这样的符号或者叫编号，这种方式是符号引用法。通过符号引用可以减少字数，使逻辑表达更清晰，符号也具有唯一性，不会出现AI引用位置出错的问题，如中文容易出现引用错误。文档中具有符号的对象有模块、表、接口、用例、页面等，AI可以通过符号查找到对应对象的描述信息。

| 对象类型 | 符号格式     | 示例     |
| ---- | -------- | ------ |
| 数据表  | TB+4 位数字 | TB0001 |
| 接口   | IF+4 位数字 | IF0002 |
| 模块   | MD+4 位数字 | MD0003 |
| 用例   | UC+4 位数字 | UC0004 |
| 页面   | PG+4 位数字 | PG0004 |

**符号引用优势**：

- 唯一性：避免中文同名对象歧义（如 "用户表" 可能指表TB0005，也可能是“用户表达意思”的前缀并不表示某张表）
- 轻量化：符号长度固定，提升文档可读性
- 机器可解析：AI 可通过符号直接关联元数据定义

### 1.3 公式表达法

- 对于涉及复杂计算的场景，可以通过变量定义 + 公式方式描述计算逻辑

**【示例】：**

- i ∈ [0,1272]
- 现金流金额[i] = TB0002.cash_val_set[i].value
- 现金流现值[i] = TB0002.present_cash_val_set[i].valueue
- 折现因子[i] = TB0003.factor_val_set[i].value
- 久期值[i] = (∑[j=i+1,1272] 现金流金额[j] \* 折现因子[j-i-1] \* (j-1/12) / (1 + 折现率[j-i-1])) / 现金流现值[i]

**说明：**

- 变量定义需包含数据来源（表名 + 字段路径）
- 公式中特殊符号需使用标准 LaTeX 格式（如∑表示求和）
- 涉及数组操作时需明确索引范围

### 1.4 标签定义法

- 对于跨记录或不同数据集间的计算场景，可以给不同数据集打上标签，再描述数据集间的关联关系，最后把数据集标签带入公式来描述计算过程

**【示例】：**
有效久期计算：
(1) 数据集标注：

- A=TB0002 (账期 = 202412,duration_type = 有效久期,bp_type=+50bp)
- B=TB0002 (账期 = 202412,duration_type = 有效久期,bp_type=-50bp)
- C=TB0002 (账期 = 202412,duration_type = 修正久期,bp_type=0bp)

(2) 关联规则：

- A/B/C 通过 [cash_flow_type,design_type,is_short_term] 进行JOIN

(3) 计算公式:

- duration_value[i]=(B.present_cash_val_set[i].value-A.present_cash_val_set[i].value)/0.01/C.present_cash_val_set[i].value,i从0开始至1272

### 1.5 SQL表达法

- 对于复杂的表关联场景，建议直接使用SQL方式描述。如果用自然语言，不仅不好表达，反而更耗时间。

### 1.6 分层分步表达法

- 在描述功能逻辑时，因涉及多步骤、多层级，可按以下分层分步方式清晰呈现：

```textile
{主步骤}：{主题描述}
({子步骤编号}) {子步骤主题}
  - {操作项1}：{详细描述}
  - {操作项2}：{详细描述}
    * {子操作1}：{技术细节}
    * {子操作2}：{技术细节}
```

**【示例】：**

```context
步骤 1：搜索请求处理
(1) 前端交互与请求发送
- 用户在电商系统前端搜索栏输入关键词（如商品名称、品类 ），点击搜索按钮后，前端页面封装搜索参数（含关键词、用户筛选条件等 ），通过 HTTP 协议向服务端发送搜索请求。
(2) 网关层请求转发
- 服务端网关接收到前端请求，校验请求合法性（如参数格式、用户身份 token 有效性 ），若合法则根据系统路由规则，将请求转发至商品搜索服务对应的业务模块。
步骤 2：搜索逻辑执行与结果返回
(1) 搜索业务逻辑处理
- 商品搜索服务模块接收请求后，先从缓存（如 Redis ）查询是否有匹配关键词的热门搜索结果缓存，若有且未过期则直接使用；若缓存无有效数据，再访问数据库（如 MySQL ），通过 SQL 语句（结合全文检索插件如 Elasticsearch 协同），根据关键词、筛选条件检索商品数据，进行数据聚合、排序（按销量、价格、新品等规则 ）。
(2) 结果封装与响应
- 搜索服务将处理后的商品数据（含商品基本信息、价格、库存状态等 ），按前端可解析的格式（如 JSON ）封装，通过网关返回给前端；前端接收后，渲染展示搜索结果列表，供用户浏览选择 。
```

## 2. 设计文档编写原则

### 2.1 尽可能使用简洁的文字描述功能逻辑

### 2.2 一句话只描述单一的功能逻辑

### 2.3 按模块间关联性拆分设计文档

### 2.4 通过AI生成90%以上代码

### 2.5 逻辑描述一定要清晰并可实现

## 3. 图表

### 3.1 流程图

**作用：**

* 以可视化方式直观呈现功能逻辑的完整脉络，让复杂逻辑结构清晰可辨

* 支持在反复迭代优化中动态调整逻辑链路，提升方案打磨效率

* 为AI生成代码提供结构化逻辑框架，辅助AI理解业务逻辑层级

**场景：**

* 当功能逻辑涉及多模块交互、分支条件或时序依赖，单纯通过文字描述难以梳理逻辑闭环时，可借助流程图进行具象化表达，同时结合文字详细描述每个节点的具体逻辑

**【示例】：**

```mermaid
flowchart TD
    A[用户登录] --> B{选择课程}
    B -->|免费课程| C[开始学习]
    B -->|付费课程| D{检查会员状态}
    D -->|是会员| C
    D -->|非会员| E[购买课程]
    E --> C

    C --> F{课程完成?}
    F -->|否| G[继续学习]
    G --> C
    F -->|是| H[参加测验]

    H --> I{测验通过?}
    I -->|是| J[获得证书]
    I -->|否| K[复习课程]
    K --> C

    J --> L[分享成绩]
    L --> M[推荐新课程]
    M --> B
```

### 3.2 协作图

**作用：**

* 清晰呈现各状态间的流转关系

**场景：**

* 适合审批状态、业务流转状态等状态描述场景

**【示例】：**

```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> Processing : Start
Processing --> Success : Success Event
Processing --> Error : Error Event
Error --> Processing : Retry
Error --> Idle : Cancel
Success --> [*]
Idle --> [*]
```