package com.xl.alm.job.ast.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xl.alm.job.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 整体资产明细表实体类
 * 对应表：t_ast_asset_detail_overall
 * 对应UC0013：整体资产明细表生成
 *
 * <AUTHOR> Assistant
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_ast_asset_detail_overall")
public class AssetDetailOverallEntity extends BaseEntity {

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 账期,格式YYYYMM */
    @TableField("accounting_period")
    private String accountingPeriod;

    /** 资产编号 */
    @TableField("asset_number")
    private Integer assetNumber;

    /** 账户名称,如传统账户、分红账户等 */
    @TableField("account_name")
    private String accountName;

    /** 证券标识代码 */
    @TableField("security_code")
    private String securityCode;

    /** 资产具体名称 */
    @TableField("asset_name")
    private String assetName;

    /** 资产小小类 */
    @TableField("asset_sub_sub_category")
    private String assetSubSubCategory;

    /** 资产大类 */
    @TableField("asset_major_category")
    private String assetMajorCategory;

    /** 境内外标识 */
    @TableField("domestic_foreign")
    private String domesticForeign;

    /** 固定收益资产细分 */
    @TableField("fixed_income_sub_category")
    private String fixedIncomeSubCategory;

    /** 资产配置状况一级分类 */
    @TableField("asset_allocation_level1")
    private String assetAllocationLevel1;

    /** 资产配置状况二级分类 */
    @TableField("asset_allocation_level2")
    private String assetAllocationLevel2;

    /** 资产配置状况三级分类 */
    @TableField("asset_allocation_level3")
    private String assetAllocationLevel3;

    /** Wind系统中的主体评级 */
    @TableField("wind_entity_rating")
    private String windEntityRating;

    /** Wind系统中的债项评级 */
    @TableField("wind_debt_rating")
    private String windDebtRating;

    /** 风险系统中的主体评级 */
    @TableField("risk_entity_rating")
    private String riskEntityRating;

    /** 风险系统中的债项评级 */
    @TableField("risk_debt_rating")
    private String riskDebtRating;

    /** 信用评级维护标识 */
    @TableField("credit_rating_maintenance")
    private String creditRatingMaintenance;

    /** 信用评级逻辑标识 */
    @TableField("credit_rating_logic_flag")
    private String creditRatingLogicFlag;

    /** 信用评级 */
    @TableField("credit_rating")
    private String creditRating;

    /** 信用评级分类 */
    @TableField("credit_rating_category")
    private String creditRatingCategory;

    /** 持仓数量 */
    @TableField("holding_quantity")
    private BigDecimal holdingQuantity;

    /** 持仓面值 */
    @TableField("holding_face_value")
    private BigDecimal holdingFaceValue;

    /** 成本金额 */
    @TableField("cost")
    private BigDecimal cost;

    /** 净价成本 */
    @TableField("net_cost")
    private BigDecimal netCost;

    /** 净价市值 */
    @TableField("net_market_value")
    private BigDecimal netMarketValue;

    /** 市值 */
    @TableField("market_value")
    private BigDecimal marketValue;

    /** 1年风险价值 */
    @TableField("var_1_year")
    private BigDecimal var1Year;

    /** 3年风险价值 */
    @TableField("var_3_year")
    private BigDecimal var3Year;

    /** 票面利率 */
    @TableField("coupon_rate")
    private BigDecimal couponRate;

    /** 年付息次数 */
    @TableField("annual_payment_frequency")
    private Integer annualPaymentFrequency;

    /** 付息方式 */
    @TableField("payment_method")
    private String paymentMethod;

    /** 起息日期 */
    @TableField("value_date")
    private Date valueDate;

    /** 买入日期 */
    @TableField("purchase_date")
    private Date purchaseDate;

    /** 到期日 */
    @TableField("maturity_date")
    private Date maturityDate;

    /** 会计分类 */
    @TableField("accounting_type")
    private String accountingType;

    /** 账面余额 */
    @TableField("book_balance")
    private BigDecimal bookBalance;

    /** 资产减值准备 */
    @TableField("asset_impairment_provision")
    private BigDecimal assetImpairmentProvision;

    /** 账面价值 */
    @TableField("book_value")
    private BigDecimal bookValue;

    /** 剩余期限 */
    @TableField("remaining_term")
    private BigDecimal remainingTerm;

    /** 剩余期限分类 */
    @TableField("remaining_term_category")
    private String remainingTermCategory;

    /** 剩余期限标识 */
    @TableField("remaining_term_flag")
    private String remainingTermFlag;

    /** 行业统计标识 */
    @TableField("industry_statistics_flag")
    private String industryStatisticsFlag;

    /** 债券类型 */
    @TableField("bond_type")
    private String bondType;

    /** 固收资产剩余期限分类 */
    @TableField("fixed_income_term_category")
    private String fixedIncomeTermCategory;

    /** 可计算现金流标识 */
    @TableField("calculable_cashflow_flag")
    private String calculableCashflowFlag;

    /** 利差久期资产统计标识 */
    @TableField("spread_duration_statistics_flag")
    private String spreadDurationStatisticsFlag;

    /** ALM资产名称 */
    @TableField("alm_asset_name")
    private String almAssetName;

    /** 单一资产统计标识 */
    @TableField("single_asset_statistics_flag")
    private String singleAssetStatisticsFlag;

    /** 五级分类 */
    @TableField("five_level_classification")
    private String fiveLevelClassification;

    /** 五级分类资产统计标识 */
    @TableField("five_level_statistics_flag")
    private String fiveLevelStatisticsFlag;

    /** 银行分类 */
    @TableField("bank_classification")
    private String bankClassification;

    /** 资产流动性分类 */
    @TableField("asset_liquidity_category")
    private String assetLiquidityCategory;

    /** 变现系数 */
    @TableField("realization_coefficient")
    private BigDecimal realizationCoefficient;

    /** 折现曲线使用评级 */
    @TableField("discount_curve_rating")
    private String discountCurveRating;

    /** 折现曲线标识 */
    @TableField("discount_curve_flag")
    private String discountCurveFlag;

    /** 调整后起息日 */
    @TableField("adjusted_value_date")
    private Date adjustedValueDate;

    /** 调整后买入日 */
    @TableField("adjusted_purchase_date")
    private Date adjustedPurchaseDate;

    /** 调整后到期日 */
    @TableField("adjusted_maturity_date")
    private Date adjustedMaturityDate;

    /** 发行时点现金流值集，JSON格式存储（临时字段，数据库中暂不存在） */
    @TableField(exist = false)
    private String issueCashflowSet;

    /** 评估时点现金流值集，JSON格式存储（临时字段，数据库中暂不存在） */
    @TableField(exist = false)
    private String evalCashflowSet;

    /** 是否删除，0:否，1:是 */
    @TableField("is_del")
    private Integer isDel = 0;
}