package com.xl.alm.job.adur.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 债券资产未来现金流计算工具类
 * 
 * 功能说明：
 * 1. 根据债券基本信息计算未来现金流
 * 2. 支持四种付息方式：到期支付、按年、按半年、按季
 * 3. 生成JSON格式的现金流数据
 * 
 * 计算逻辑：
 * - 仅处理"可计算现金流固收资产标识"=1且"剩余期限分类"≠1的资产
 * - 仅计算调整起息日之后、调整到期日之前的现金流
 * - 根据付息方式和总月数计算利息和本金偿还
 *
 * <AUTHOR> Assistant
 */
@Slf4j
public class BondCashFlowCalculator {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 付息方式枚举
     */
    public enum PaymentMethod {
        MATURITY_PAYMENT(0, "到期一次性支付"),
        ANNUAL_PAYMENT(1, "按年支付利息"),
        SEMI_ANNUAL_PAYMENT(2, "按半年支付利息"),
        QUARTERLY_PAYMENT(4, "按季支付利息");

        private final int code;
        private final String description;

        PaymentMethod(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static PaymentMethod fromCode(int code) {
            for (PaymentMethod method : values()) {
                if (method.code == code) {
                    return method;
                }
            }
            throw new IllegalArgumentException("未知的付息方式代码: " + code);
        }
    }

    /**
     * 现金流项目
     */
    public static class CashFlowItem {
        private int period;
        private String date;
        private BigDecimal amount;
        private String type;

        public CashFlowItem(int period, String date, BigDecimal amount, String type) {
            this.period = period;
            this.date = date;
            this.amount = amount;
            this.type = type;
        }

        // Getters and Setters
        public int getPeriod() { return period; }
        public void setPeriod(int period) { this.period = period; }
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    /**
     * 现金流结果
     */
    public static class CashFlowResult {
        private Integer assetNumber;
        private String calculationDate;
        private List<CashFlowItem> cashFlows;
        private int totalPeriods;
        private int paymentFrequency;

        public CashFlowResult() {
            this.cashFlows = new ArrayList<>();
        }

        // Getters and Setters
        public Integer getAssetNumber() { return assetNumber; }
        public void setAssetNumber(Integer assetNumber) { this.assetNumber = assetNumber; }
        public String getCalculationDate() { return calculationDate; }
        public void setCalculationDate(String calculationDate) { this.calculationDate = calculationDate; }
        public List<CashFlowItem> getCashFlows() { return cashFlows; }
        public void setCashFlows(List<CashFlowItem> cashFlows) { this.cashFlows = cashFlows; }
        public int getTotalPeriods() { return totalPeriods; }
        public void setTotalPeriods(int totalPeriods) { this.totalPeriods = totalPeriods; }
        public int getPaymentFrequency() { return paymentFrequency; }
        public void setPaymentFrequency(int paymentFrequency) { this.paymentFrequency = paymentFrequency; }
    }

    /**
     * 计算债券现金流
     *
     * @param assetNumber 资产编号
     * @param holdingFaceValue 持仓面值
     * @param couponRate 票面利率
     * @param paymentMethodCode 付息方式代码
     * @param adjustedValueDate 调整起息日
     * @param adjustedMaturityDate 调整到期日
     * @param calculationBaseDate 计算基准日期
     * @return 现金流结果
     */
    public static CashFlowResult calculateCashFlow(Integer assetNumber,
                                                   BigDecimal holdingFaceValue,
                                                   BigDecimal couponRate,
                                                   int paymentMethodCode,
                                                   LocalDate adjustedValueDate,
                                                   LocalDate adjustedMaturityDate,
                                                   LocalDate calculationBaseDate) {

        CashFlowResult result = new CashFlowResult();
        result.setAssetNumber(assetNumber);
        result.setCalculationDate(calculationBaseDate.format(DATE_FORMATTER));

        PaymentMethod paymentMethod = PaymentMethod.fromCode(paymentMethodCode);
        result.setPaymentFrequency(paymentMethod.getCode());

        // 计算总月数
        long totalMonths = ChronoUnit.MONTHS.between(adjustedValueDate, adjustedMaturityDate);
        result.setTotalPeriods((int) totalMonths);

        // 计算付息月份
        List<Integer> paymentMonths = calculatePaymentMonths(paymentMethod, adjustedValueDate, adjustedMaturityDate);

        // 生成现金流
        generateCashFlows(result, paymentMethod, holdingFaceValue, couponRate,
                         adjustedValueDate, adjustedMaturityDate, calculationBaseDate,
                         paymentMonths, totalMonths);

        return result;
    }

    /**
     * 计算债券现金流（重载方法，支持直接传入数据库字典值）
     *
     * @param assetNumber 资产编号
     * @param holdingFaceValue 持仓面值
     * @param couponRate 票面利率
     * @param paymentMethodDictValue 付息方式字典值（从t_ast_asset_detail_overall表的payment_method字段读取）
     * @param adjustedValueDate 调整起息日
     * @param adjustedMaturityDate 调整到期日
     * @param calculationBaseDate 计算基准日期
     * @return 现金流结果
     */
    public static CashFlowResult calculateCashFlowWithDictValue(Integer assetNumber,
                                                               BigDecimal holdingFaceValue,
                                                               BigDecimal couponRate,
                                                               String paymentMethodDictValue,
                                                               LocalDate adjustedValueDate,
                                                               LocalDate adjustedMaturityDate,
                                                               LocalDate calculationBaseDate) {

        CashFlowResult result = new CashFlowResult();
        result.setAssetNumber(assetNumber);
        result.setCalculationDate(calculationBaseDate.format(DATE_FORMATTER));

        // 直接设置数据库中的字典值作为PaymentFrequency
        result.setPaymentFrequency(parsePaymentMethodDictValue(paymentMethodDictValue));

        // 计算总月数
        long totalMonths = ChronoUnit.MONTHS.between(adjustedValueDate, adjustedMaturityDate);
        result.setTotalPeriods((int) totalMonths);

        // 计算付息月份
        List<Integer> paymentMonths = calculatePaymentMonthsByDictValue(paymentMethodDictValue, adjustedValueDate, adjustedMaturityDate);

        // 生成现金流
        generateCashFlowsByDictValue(result, paymentMethodDictValue, holdingFaceValue, couponRate,
                                   adjustedValueDate, adjustedMaturityDate, calculationBaseDate,
                                   paymentMonths, totalMonths);

        return result;
    }

    /**
     * 解析付息方式字典值为整数（用于PaymentFrequency字段）
     *
     * @param paymentMethodDictValue 付息方式字典值
     * @return 整数值
     */
    private static int parsePaymentMethodDictValue(String paymentMethodDictValue) {
        if (!StringUtils.hasText(paymentMethodDictValue)) {
            return 5; // 默认为"05"到期一次性付息
        }

        try {
            return Integer.parseInt(paymentMethodDictValue.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析付息方式字典值：{}，使用默认值05", paymentMethodDictValue);
            return 5;
        }
    }



    /**
     * 根据字典值计算付息月份
     *
     * 付息月份计算逻辑：
     * 1. 到期支付（05）：付息月份一 = 调整到期日的月份
     * 2. 按年支付（01）：付息月份一 = 调整起息日的月份
     * 3. 按半年支付（02）：
     *    - 付息月份一 = 调整起息日月份 +6（若超过12则减12）
     *    - 付息月份二 = 调整起息日的月份
     * 4. 按季支付（03）：
     *    - 付息月份一 = 调整起息日月份 +3（若超过12则减12）
     *    - 付息月份二 = 调整起息日月份 +6（若超过12则减12）
     *    - 付息月份三 = 调整起息日月份 +9（若超过12则减12）
     *    - 付息月份四 = 调整起息日的月份
     */
    private static List<Integer> calculatePaymentMonthsByDictValue(String paymentMethodDictValue,
                                                                  LocalDate adjustedValueDate,
                                                                  LocalDate adjustedMaturityDate) {
        List<Integer> paymentMonths = new ArrayList<>();
        int valueMonth = adjustedValueDate.getMonthValue();
        int maturityMonth = adjustedMaturityDate.getMonthValue();

        if (!StringUtils.hasText(paymentMethodDictValue)) {
            paymentMethodDictValue = "05"; // 默认到期支付
        }

        switch (paymentMethodDictValue.trim()) {
            case "01": // 到期一次性支付
            case "05": // 兼容旧的"05"值
                // 付息月份一 = 调整到期日的月份
                paymentMonths.add(maturityMonth);
                break;

            case "02": // 按年支付
                // 付息月份一 = 调整起息日的月份
                paymentMonths.add(valueMonth);
                break;

            case "03": // 按半年支付
                // 付息月份一 = 调整起息日月份 +6（若超过12则减12）
                int semiMonth1 = (valueMonth + 6 > 12) ? valueMonth + 6 - 12 : valueMonth + 6;
                paymentMonths.add(semiMonth1);
                // 付息月份二 = 调整起息日的月份
                paymentMonths.add(valueMonth);
                break;

            case "04": // 按季支付
                // 付息月份一 = 调整起息日月份 +3（若超过12则减12）
                int quarterMonth1 = (valueMonth + 3 > 12) ? valueMonth + 3 - 12 : valueMonth + 3;
                paymentMonths.add(quarterMonth1);
                // 付息月份二 = 调整起息日月份 +6（若超过12则减12）
                int quarterMonth2 = (valueMonth + 6 > 12) ? valueMonth + 6 - 12 : valueMonth + 6;
                paymentMonths.add(quarterMonth2);
                // 付息月份三 = 调整起息日月份 +9（若超过12则减12）
                int quarterMonth3 = (valueMonth + 9 > 12) ? valueMonth + 9 - 12 : valueMonth + 9;
                paymentMonths.add(quarterMonth3);
                // 付息月份四 = 调整起息日的月份
                paymentMonths.add(valueMonth);
                break;

            default:
                log.warn("未知的付息方式字典值：{}，使用到期支付", paymentMethodDictValue);
                paymentMonths.add(maturityMonth);
                break;
        }

        return paymentMonths;
    }



    /**
     * 计算付息月份（原方法保留用于兼容性）
     */
    private static List<Integer> calculatePaymentMonths(PaymentMethod paymentMethod,
                                                       LocalDate adjustedValueDate,
                                                       LocalDate adjustedMaturityDate) {
        List<Integer> paymentMonths = new ArrayList<>();
        int valueMonth = adjustedValueDate.getMonthValue();
        int maturityMonth = adjustedMaturityDate.getMonthValue();

        switch (paymentMethod) {
            case MATURITY_PAYMENT:
                paymentMonths.add(maturityMonth);
                break;
            case ANNUAL_PAYMENT:
                paymentMonths.add(valueMonth);
                break;
            case SEMI_ANNUAL_PAYMENT:
                paymentMonths.add((valueMonth + 6 > 12) ? valueMonth + 6 - 12 : valueMonth + 6);
                paymentMonths.add(valueMonth);
                break;
            case QUARTERLY_PAYMENT:
                paymentMonths.add((valueMonth + 3 > 12) ? valueMonth + 3 - 12 : valueMonth + 3);
                paymentMonths.add((valueMonth + 6 > 12) ? valueMonth + 6 - 12 : valueMonth + 6);
                paymentMonths.add((valueMonth + 9 > 12) ? valueMonth + 9 - 12 : valueMonth + 9);
                paymentMonths.add(valueMonth);
                break;
        }

        return paymentMonths;
    }

    /**
     * 根据字典值生成现金流
     *
     * 按照债券资产未来现金流计算逻辑：
     * - 仅处理调整起息日之后、调整到期日之前的现金流
     * -
     */
    private static void generateCashFlowsByDictValue(CashFlowResult result,
                                                    String paymentMethodDictValue,
                                                    BigDecimal holdingFaceValue,
                                                    BigDecimal couponRate,
                                                    LocalDate adjustedValueDate,
                                                    LocalDate adjustedMaturityDate,
                                                    LocalDate calculationBaseDate,
                                                    List<Integer> paymentMonths,
                                                    long totalMonths) {

        if (!StringUtils.hasText(paymentMethodDictValue)) {
            paymentMethodDictValue = "01"; // 默认到期支付
        }

        // 生成0-600期的现金流（总共601期）
        for (int period = 0; period <= 600; period++) {
            // 确保每个期间的日期都是对应月份的最后一天
            LocalDate futureDate = getLastDayOfMonth(calculationBaseDate.plusMonths(period));
            BigDecimal cashFlowAmount = BigDecimal.ZERO;
            String cashFlowType = "none";

            // 期限0（计算基准日当天）通常现金流为0
            if (period == 0) {
                cashFlowAmount = BigDecimal.ZERO;
                cashFlowType = "base_date";
            }
            // 检查是否在起息日之后、到期日之前（包含到期日）
            else if (futureDate.isAfter(adjustedValueDate) && !futureDate.isAfter(adjustedMaturityDate)) {

                // 到期日处理
                if (futureDate.equals(adjustedMaturityDate)) {
                    cashFlowAmount = calculateMaturityCashFlowByDictValue(paymentMethodDictValue, holdingFaceValue,
                                                                        couponRate, totalMonths);
                    cashFlowType = "principal_and_interest";
                }
                // 付息日处理（到期支付方式不在付息日产生现金流）
                else if (!"01".equals(paymentMethodDictValue.trim()) && !"05".equals(paymentMethodDictValue.trim()) &&
                         isPaymentDateByDictValue(futureDate, paymentMonths, paymentMethodDictValue,
                                                adjustedValueDate, adjustedMaturityDate)) {
                    cashFlowAmount = calculateInterestCashFlowByDictValue(paymentMethodDictValue, holdingFaceValue, couponRate);
                    cashFlowType = "interest";
                }
            }

            // 添加现金流项目（包括金额为0的项目，用于完整的0-600期数据）
            result.getCashFlows().add(new CashFlowItem(period, futureDate.format(DATE_FORMATTER),
                                                      cashFlowAmount, cashFlowType));
        }
    }

    /**
     * 生成现金流（原方法保留用于兼容性）
     */
    private static void generateCashFlows(CashFlowResult result,
                                        PaymentMethod paymentMethod,
                                        BigDecimal holdingFaceValue,
                                        BigDecimal couponRate,
                                        LocalDate adjustedValueDate,
                                        LocalDate adjustedMaturityDate,
                                        LocalDate calculationBaseDate,
                                        List<Integer> paymentMonths,
                                        long totalMonths) {

        // 计算付息周期
        int paymentCycle = getPaymentCycle(paymentMethod);
        boolean isIntegerCycle = (totalMonths % paymentCycle == 0);
        int remainderMonths = (int) (totalMonths % paymentCycle);

        // 生成0-600期的现金流（总共601期）
        for (int period = 0; period <= 600; period++) {
            // 确保每个期间的日期都是对应月份的最后一天
            LocalDate futureDate = getLastDayOfMonth(calculationBaseDate.plusMonths(period));
            BigDecimal cashFlowAmount = BigDecimal.ZERO;
            String cashFlowType = "none";

            // 期限0（计算基准日当天）通常现金流为0
            if (period == 0) {
                cashFlowAmount = BigDecimal.ZERO;
                cashFlowType = "base_date";
            }
            // 检查是否在起息日之后、到期日之前
            else if (futureDate.isAfter(adjustedValueDate) && !futureDate.isAfter(adjustedMaturityDate)) {

                // 到期日处理
                if (futureDate.equals(adjustedMaturityDate)) {
                    cashFlowAmount = calculateMaturityCashFlow(paymentMethod, holdingFaceValue,
                                                             couponRate, totalMonths);
                    cashFlowType = "principal_and_interest";
                }
                // 付息日处理（到期支付方式不在付息日产生现金流）
                else if (paymentMethod != PaymentMethod.MATURITY_PAYMENT &&
                         isPaymentDate(futureDate, paymentMonths, adjustedValueDate, adjustedMaturityDate)) {
                    cashFlowAmount = calculateInterestCashFlow(paymentMethod, holdingFaceValue, couponRate);
                    cashFlowType = "interest";
                }
            }

            // 添加现金流项目（包括金额为0的项目，用于完整的0-600期数据）
            result.getCashFlows().add(new CashFlowItem(period, futureDate.format(DATE_FORMATTER),
                                                      cashFlowAmount, cashFlowType));
        }
    }

    /**
     * 获取付息周期（月数）
     */
    private static int getPaymentCycle(PaymentMethod paymentMethod) {
        switch (paymentMethod) {
            case MATURITY_PAYMENT: return (int) Long.MAX_VALUE; // 特殊处理
            case ANNUAL_PAYMENT: return 12;
            case SEMI_ANNUAL_PAYMENT: return 6;
            case QUARTERLY_PAYMENT: return 3;
            default: throw new IllegalArgumentException("未知的付息方式");
        }
    }

    /**
     * 根据字典值计算到期日现金流
     *
     * 根据付息方式字典值和总月数计算到期日现金流：
     * 1. 到期支付（01）：现金流 = 持仓面值 × (1 + 票面利率/12 × 总月数)
     * 2. 按年支付（02）：
     *    - 总月数是12的整数倍：现金流 = 持仓面值 × (1 + 票面利率)
     *    - 总月数非12的整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
     * 3. 按半年支付（03）：
     *    - 总月数是6的整数倍：现金流 = 持仓面值 × (1 + 票面利率/2)
     *    - 总月数非6的整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
     * 4. 按季支付（04）：
     *    - 总月数是3的整数倍：现金流 = 持仓面值 × (1 + 票面利率/4)
     *    - 总月数非3的整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
     */
    private static BigDecimal calculateMaturityCashFlowByDictValue(String paymentMethodDictValue,
                                                                  BigDecimal holdingFaceValue,
                                                                  BigDecimal couponRate,
                                                                  long totalMonths) {

        if (!StringUtils.hasText(paymentMethodDictValue)) {
            paymentMethodDictValue = "01"; // 默认到期支付
        }

        switch (paymentMethodDictValue.trim()) {
            case "01": // 到期一次性支付
            case "05": // 兼容旧的"05"值
                // 现金流 = 持仓面值 × (1 + 票面利率 × 总月数 / 12)
                // 先乘后除，避免除法精度损失
                BigDecimal totalInterestNumerator = couponRate.multiply(BigDecimal.valueOf(totalMonths));
                BigDecimal totalInterest = totalInterestNumerator.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                return holdingFaceValue.multiply(BigDecimal.ONE.add(totalInterest));

            case "02": // 按年支付
                if (totalMonths % 12 == 0) {
                    // 总月数是12的整数倍：现金流 = 持仓面值 × (1 + 票面利率)
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(couponRate));
                } else {
                    // 总月数非12的整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
                    int remainderMonths = (int) (totalMonths % 12);
                    BigDecimal monthlyRate12 = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                    BigDecimal remainderInterest = monthlyRate12.multiply(BigDecimal.valueOf(remainderMonths));
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(remainderInterest));
                }

            case "03": // 按半年支付
                if (totalMonths % 6 == 0) {
                    // 总月数是6的整数倍：现金流 = 持仓面值 × (1 + 票面利率/2)
                    BigDecimal semiAnnualRate = couponRate.divide(BigDecimal.valueOf(2), 10, RoundingMode.HALF_UP);
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(semiAnnualRate));
                } else {
                    // 总月数非6的整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
                    int remainderMonths = (int) (totalMonths % 6);
                    BigDecimal monthlyRate6 = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                    BigDecimal remainderInterest = monthlyRate6.multiply(BigDecimal.valueOf(remainderMonths));
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(remainderInterest));
                }

            case "04": // 按季支付
                if (totalMonths % 3 == 0) {
                    // 总月数是3的整数倍：现金流 = 持仓面值 × (1 + 票面利率/4)
                    BigDecimal quarterlyRate = couponRate.divide(BigDecimal.valueOf(4), 10, RoundingMode.HALF_UP);
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(quarterlyRate));
                } else {
                    // 总月数非3的整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
                    int remainderMonths = (int) (totalMonths % 3);
                    BigDecimal monthlyRate3 = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                    BigDecimal remainderInterest = monthlyRate3.multiply(BigDecimal.valueOf(remainderMonths));
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(remainderInterest));
                }

            default:
                log.warn("未知的付息方式字典值：{}，使用到期支付", paymentMethodDictValue);
                BigDecimal defaultMonthlyRate = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                BigDecimal defaultTotalInterest = defaultMonthlyRate.multiply(BigDecimal.valueOf(totalMonths));
                return holdingFaceValue.multiply(BigDecimal.ONE.add(defaultTotalInterest));
        }
    }

    /**
     * 计算到期日现金流（原方法保留用于兼容性）
     */
    private static BigDecimal calculateMaturityCashFlow(PaymentMethod paymentMethod,
                                                       BigDecimal holdingFaceValue,
                                                       BigDecimal couponRate,
                                                       long totalMonths) {

        switch (paymentMethod) {
            case MATURITY_PAYMENT:
                // 到期支付：现金流 = 持仓面值 × (1 + 票面利率/12 × 总月数)
                BigDecimal monthlyRate = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                BigDecimal totalInterest = monthlyRate.multiply(BigDecimal.valueOf(totalMonths));
                return holdingFaceValue.multiply(BigDecimal.ONE.add(totalInterest));

            case ANNUAL_PAYMENT:
                // 按年支付
                if (totalMonths % 12 == 0) {
                    // 整数倍：现金流 = 持仓面值 × (1 + 票面利率)
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(couponRate));
                } else {
                    // 非整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
                    int remainderMonths = (int) (totalMonths % 12);
                    BigDecimal monthlyRate12 = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                    BigDecimal remainderInterest = monthlyRate12.multiply(BigDecimal.valueOf(remainderMonths));
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(remainderInterest));
                }

            case SEMI_ANNUAL_PAYMENT:
                // 按半年支付
                if (totalMonths % 6 == 0) {
                    // 整数倍：现金流 = 持仓面值 × (1 + 票面利率/2)
                    BigDecimal semiAnnualRate = couponRate.divide(BigDecimal.valueOf(2), 10, RoundingMode.HALF_UP);
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(semiAnnualRate));
                } else {
                    // 非整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
                    int remainderMonths = (int) (totalMonths % 6);
                    BigDecimal monthlyRate6 = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                    BigDecimal remainderInterest = monthlyRate6.multiply(BigDecimal.valueOf(remainderMonths));
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(remainderInterest));
                }

            case QUARTERLY_PAYMENT:
                // 按季支付
                if (totalMonths % 3 == 0) {
                    // 整数倍：现金流 = 持仓面值 × (1 + 票面利率/4)
                    BigDecimal quarterlyRate = couponRate.divide(BigDecimal.valueOf(4), 10, RoundingMode.HALF_UP);
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(quarterlyRate));
                } else {
                    // 非整数倍：现金流 = 持仓面值 × (1 + 票面利率/12 × 余数月数)
                    int remainderMonths = (int) (totalMonths % 3);
                    BigDecimal monthlyRate3 = couponRate.divide(BigDecimal.valueOf(12), 10, RoundingMode.HALF_UP);
                    BigDecimal remainderInterest = monthlyRate3.multiply(BigDecimal.valueOf(remainderMonths));
                    return holdingFaceValue.multiply(BigDecimal.ONE.add(remainderInterest));
                }

            default:
                return holdingFaceValue;
        }
    }

    /**
     * 根据字典值计算利息现金流
     *
     * 根据付息方式字典值计算付息日的利息现金流：
     * 1. 到期支付（01）：不产生利息现金流（所有利息在到期日支付）
     * 2. 按年支付（02）：现金流 = 持仓面值 × 票面利率
     * 3. 按半年支付（03）：现金流 = 持仓面值 × 票面利率/2
     * 4. 按季支付（04）：现金流 = 持仓面值 × 票面利率/4
     */
    private static BigDecimal calculateInterestCashFlowByDictValue(String paymentMethodDictValue,
                                                                  BigDecimal holdingFaceValue,
                                                                  BigDecimal couponRate) {

        if (!StringUtils.hasText(paymentMethodDictValue)) {
            return BigDecimal.ZERO;
        }

        switch (paymentMethodDictValue.trim()) {
            case "01": // 到期一次性支付
            case "05": // 兼容旧的"05"值
                // 不产生利息现金流
                return BigDecimal.ZERO;
            case "02": // 按年支付
                // 现金流 = 持仓面值 × 票面利率
                return holdingFaceValue.multiply(couponRate);
            case "03": // 按半年支付
                // 现金流 = 持仓面值 × 票面利率/2
                return holdingFaceValue.multiply(couponRate).divide(BigDecimal.valueOf(2), 10, RoundingMode.HALF_UP);
            case "04": // 按季支付
                // 现金流 = 持仓面值 × 票面利率/4
                return holdingFaceValue.multiply(couponRate).divide(BigDecimal.valueOf(4), 10, RoundingMode.HALF_UP);
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 计算利息现金流（原方法保留用于兼容性）
     */
    private static BigDecimal calculateInterestCashFlow(PaymentMethod paymentMethod,
                                                       BigDecimal holdingFaceValue,
                                                       BigDecimal couponRate) {
        switch (paymentMethod) {
            case ANNUAL_PAYMENT:
                // 按年支付：现金流 = 持仓面值 × 票面利率
                return holdingFaceValue.multiply(couponRate);
            case SEMI_ANNUAL_PAYMENT:
                // 按半年支付：现金流 = 持仓面值 × 票面利率/2
                return holdingFaceValue.multiply(couponRate).divide(BigDecimal.valueOf(2), 10, RoundingMode.HALF_UP);
            case QUARTERLY_PAYMENT:
                // 按季支付：现金流 = 持仓面值 × 票面利率/4
                return holdingFaceValue.multiply(couponRate).divide(BigDecimal.valueOf(4), 10, RoundingMode.HALF_UP);
            case MATURITY_PAYMENT:
                // 到期支付：不产生利息现金流
                return BigDecimal.ZERO;
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 根据字典值判断是否为付息日期
     *
     * 付息日期需同时满足：
     * 1. 月份匹配付息月份
     * 2. 年份在起息日与到期日之间
     * 3. 不是到期日（到期日单独处理）
     */
    private static boolean isPaymentDateByDictValue(LocalDate date, List<Integer> paymentMonths, String paymentMethodDictValue,
                                                   LocalDate adjustedValueDate, LocalDate adjustedMaturityDate) {
        int dateMonth = date.getMonthValue();

        // 检查月份是否匹配
        if (!paymentMonths.contains(dateMonth)) {
            return false;
        }

        // 检查是否在有效期间内（不包括到期日，因为到期日单独处理）
        if (!date.isAfter(adjustedValueDate) || !date.isBefore(adjustedMaturityDate)) {
            return false;
        }

        // 到期支付方式不在付息日产生现金流
        if ("01".equals(paymentMethodDictValue.trim()) || "05".equals(paymentMethodDictValue.trim())) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否为付息日期（原方法保留用于兼容性）
     */
    private static boolean isPaymentDate(LocalDate date, List<Integer> paymentMonths,
                                       LocalDate adjustedValueDate, LocalDate adjustedMaturityDate) {
        int month = date.getMonthValue();
        return paymentMonths.contains(month) &&
               date.isAfter(adjustedValueDate) &&
               date.isBefore(adjustedMaturityDate);
    }

    /**
     * 将现金流结果转换为JSON字符串
     * 新格式：{"0":{"date":"2023/06/30","value":"2100"},"1":{"date":"2023/07/31","value":"2100"}}
     * 按期限顺序排序（0, 1, 2, ..., 600）
     */
    public static String toJson(CashFlowResult cashFlowResult) {
        try {
            // 使用TreeMap确保按期限数字顺序排序
            Map<Integer, Map<String, String>> sortedMap = new TreeMap<>();

            if (cashFlowResult != null && cashFlowResult.getCashFlows() != null) {
                for (CashFlowItem item : cashFlowResult.getCashFlows()) {
                    Map<String, String> itemMap = new LinkedHashMap<>(); // 保持内部字段顺序
                    itemMap.put("date", item.getDate());
                    itemMap.put("value", item.getAmount().toString());

                    sortedMap.put(item.getPeriod(), itemMap);
                }
            }

            // 转换为字符串键的LinkedHashMap以保持顺序
            Map<String, Map<String, String>> resultMap = new LinkedHashMap<>();
            for (Map.Entry<Integer, Map<String, String>> entry : sortedMap.entrySet()) {
                resultMap.put(String.valueOf(entry.getKey()), entry.getValue());
            }

            return objectMapper.writeValueAsString(resultMap);
        } catch (Exception e) {
            log.error("现金流结果转换为JSON失败", e);
            return "{}";
        }
    }

    /**
     * 从JSON字符串解析现金流结果
     * 支持新格式：{"0":{"日期":"2023/06/30","值":"2100"},"1":{"日期":"2023/07/31","值":"2100"}}
     */
    public static CashFlowResult fromJson(String json) {
        try {
            CashFlowResult result = new CashFlowResult();

            if (json == null || json.trim().isEmpty() || "{}".equals(json.trim())) {
                return result;
            }

            // 解析新格式的JSON
            TypeReference<Map<String, Map<String, String>>> typeRef = new TypeReference<Map<String, Map<String, String>>>() {};
            Map<String, Map<String, String>> jsonMap = objectMapper.readValue(json, typeRef);

            List<CashFlowItem> cashFlows = new ArrayList<>();
            for (Map.Entry<String, Map<String, String>> entry : jsonMap.entrySet()) {
                int period = Integer.parseInt(entry.getKey());
                Map<String, String> itemData = entry.getValue();

                String date = itemData.get("date");
                String amountStr = itemData.get("value");
                BigDecimal amount = new BigDecimal(amountStr);

                cashFlows.add(new CashFlowItem(period, date, amount, ""));
            }

            result.setCashFlows(cashFlows);
            return result;

        } catch (Exception e) {
            log.error("JSON解析为现金流结果失败: {}", json, e);
            return new CashFlowResult();
        }
    }

    /**
     * 获取指定日期所在月份的最后一天
     *
     * @param date 指定日期
     * @return 该月份的最后一天
     */
    private static LocalDate getLastDayOfMonth(LocalDate date) {
        return date.withDayOfMonth(date.lengthOfMonth());
    }
}
