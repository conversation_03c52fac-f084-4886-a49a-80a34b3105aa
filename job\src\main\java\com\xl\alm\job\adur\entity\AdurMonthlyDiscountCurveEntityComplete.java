package com.xl.alm.job.adur.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xl.alm.job.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ADUR月度折现曲线表不含价差完整实体类
 * 对应表：t_adur_monthly_discount_curve
 * 包含完整的term_0到term_600共601个期限字段
 * 用于UC0005：计算月度折现曲线不含价差
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_adur_monthly_discount_curve")
public class AdurMonthlyDiscountCurveEntityComplete extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId
    @TableField("id")
    private Long id;

    /**
     * 账期
     */
    @TableField("account_period")
    private String accountPeriod;

    /**
     * 日期类型
     */
    @TableField("date_type")
    private String dateType;

    /**
     * 日期
     */
    @TableField("date")
    private Date date;

    /**
     * 资产编号
     */
    @TableField("asset_number")
    private Integer assetNumber;

    /**
     * 账户名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 资产名称
     */
    @TableField("asset_name")
    private String assetName;

    /**
     * 证券代码
     */
    @TableField("security_code")
    private String securityCode;

    /**
     * 折现曲线标识
     */
    @TableField("curve_id")
    private String curveId;

    // ========== 期限字段：term_0到term_600共601个字段 ==========

    /**
     * 期限0
     */
    @TableField("term_0")
    private BigDecimal term0;

    /**
     * 期限1
     */
    @TableField("term_1")
    private BigDecimal term1;

    /**
     * 期限2
     */
    @TableField("term_2")
    private BigDecimal term2;

    /**
     * 期限3
     */
    @TableField("term_3")
    private BigDecimal term3;

    /**
     * 期限4
     */
    @TableField("term_4")
    private BigDecimal term4;

    /**
     * 期限5
     */
    @TableField("term_5")
    private BigDecimal term5;

    /**
     * 期限6
     */
    @TableField("term_6")
    private BigDecimal term6;

    /**
     * 期限7
     */
    @TableField("term_7")
    private BigDecimal term7;

    /**
     * 期限8
     */
    @TableField("term_8")
    private BigDecimal term8;

    /**
     * 期限9
     */
    @TableField("term_9")
    private BigDecimal term9;

    /**
     * 期限10
     */
    @TableField("term_10")
    private BigDecimal term10;

    /**
     * 期限11
     */
    @TableField("term_11")
    private BigDecimal term11;

    /**
     * 期限12
     */
    @TableField("term_12")
    private BigDecimal term12;

    /**
     * 期限13
     */
    @TableField("term_13")
    private BigDecimal term13;

    /**
     * 期限14
     */
    @TableField("term_14")
    private BigDecimal term14;

    /**
     * 期限15
     */
    @TableField("term_15")
    private BigDecimal term15;

    /**
     * 期限16
     */
    @TableField("term_16")
    private BigDecimal term16;

    /**
     * 期限17
     */
    @TableField("term_17")
    private BigDecimal term17;

    /**
     * 期限18
     */
    @TableField("term_18")
    private BigDecimal term18;

    /**
     * 期限19
     */
    @TableField("term_19")
    private BigDecimal term19;

    /**
     * 期限20
     */
    @TableField("term_20")
    private BigDecimal term20;

    /**
     * 期限21
     */
    @TableField("term_21")
    private BigDecimal term21;

    /**
     * 期限22
     */
    @TableField("term_22")
    private BigDecimal term22;

    /**
     * 期限23
     */
    @TableField("term_23")
    private BigDecimal term23;

    /**
     * 期限24
     */
    @TableField("term_24")
    private BigDecimal term24;

    /**
     * 期限25
     */
    @TableField("term_25")
    private BigDecimal term25;

    /**
     * 期限26
     */
    @TableField("term_26")
    private BigDecimal term26;

    /**
     * 期限27
     */
    @TableField("term_27")
    private BigDecimal term27;

    /**
     * 期限28
     */
    @TableField("term_28")
    private BigDecimal term28;

    /**
     * 期限29
     */
    @TableField("term_29")
    private BigDecimal term29;

    /**
     * 期限30
     */
    @TableField("term_30")
    private BigDecimal term30;

    /**
     * 期限31
     */
    @TableField("term_31")
    private BigDecimal term31;

    /**
     * 期限32
     */
    @TableField("term_32")
    private BigDecimal term32;

    /**
     * 期限33
     */
    @TableField("term_33")
    private BigDecimal term33;

    /**
     * 期限34
     */
    @TableField("term_34")
    private BigDecimal term34;

    /**
     * 期限35
     */
    @TableField("term_35")
    private BigDecimal term35;

    /**
     * 期限36
     */
    @TableField("term_36")
    private BigDecimal term36;

    /**
     * 期限37
     */
    @TableField("term_37")
    private BigDecimal term37;

    /**
     * 期限38
     */
    @TableField("term_38")
    private BigDecimal term38;

    /**
     * 期限39
     */
    @TableField("term_39")
    private BigDecimal term39;

    /**
     * 期限40
     */
    @TableField("term_40")
    private BigDecimal term40;

    /**
     * 期限41
     */
    @TableField("term_41")
    private BigDecimal term41;

    /**
     * 期限42
     */
    @TableField("term_42")
    private BigDecimal term42;

    /**
     * 期限43
     */
    @TableField("term_43")
    private BigDecimal term43;

    /**
     * 期限44
     */
    @TableField("term_44")
    private BigDecimal term44;

    /**
     * 期限45
     */
    @TableField("term_45")
    private BigDecimal term45;

    /**
     * 期限46
     */
    @TableField("term_46")
    private BigDecimal term46;

    /**
     * 期限47
     */
    @TableField("term_47")
    private BigDecimal term47;

    /**
     * 期限48
     */
    @TableField("term_48")
    private BigDecimal term48;

    /**
     * 期限49
     */
    @TableField("term_49")
    private BigDecimal term49;

    /**
     * 期限50
     */
    @TableField("term_50")
    private BigDecimal term50;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_del")
    private Integer isDel = 0;
}
