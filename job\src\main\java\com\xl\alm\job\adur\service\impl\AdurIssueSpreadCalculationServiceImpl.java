package com.xl.alm.job.adur.service.impl;

import com.xl.alm.job.adur.entity.AdurDurationAssetDetailEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountFactorWithSpreadEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity;
import com.xl.alm.job.adur.mapper.AdurDurationAssetDetailMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountFactorWithSpreadMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveWithSpreadMapper;
import com.xl.alm.job.adur.service.AdurIssueSpreadCalculationService;
import com.xl.alm.job.adur.util.TermDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * ADUR发行时点价差计算服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurIssueSpreadCalculationServiceImpl implements AdurIssueSpreadCalculationService {

    /** 曲线细分类：发行时点价差计算使用曲线细分类=1 */
    private static final String CURVE_SUB_CATEGORY_ISSUE = "1";

    /** 曲线细分类：评估时点价差计算使用曲线细分类=5 */
    private static final String CURVE_SUB_CATEGORY_EVAL = "5";

    @Autowired
    private AdurDurationAssetDetailMapper durationAssetDetailMapper;

    @Autowired
    private AdurMonthlyDiscountFactorWithSpreadMapper monthlyDiscountFactorWithSpreadMapper;

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadMapper monthlyDiscountCurveWithSpreadMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateIssueSpread(String accountPeriod) {
        log.info("开始计算发行时点价差，账期：{}", accountPeriod);

        try {
            // 1. 查询久期资产明细数据
            List<AdurDurationAssetDetailEntity> assetDetailList = durationAssetDetailMapper.selectByAccountPeriod(accountPeriod);
            if (CollectionUtils.isEmpty(assetDetailList)) {
                log.warn("未找到账期{}的久期资产明细数据", accountPeriod);
                return true; // 没有数据也算成功
            }

            // 2. 查询月度折现曲线表含价差数据（曲线细分类=1）
            List<AdurMonthlyDiscountCurveWithSpreadEntity> discountCurveList =
                    monthlyDiscountCurveWithSpreadMapper.selectByAccountPeriod(accountPeriod);
            if (CollectionUtils.isEmpty(discountCurveList)) {
                log.warn("未找到账期{}的月度折现曲线表含价差数据", accountPeriod);
                return false;
            }

            // 3. 查询月度折现因子表含价差数据（用于更新）
            List<AdurMonthlyDiscountFactorWithSpreadEntity> discountFactorList =
                    monthlyDiscountFactorWithSpreadMapper.selectByAccountPeriod(accountPeriod);

            int successCount = 0;
            int totalCount = assetDetailList.size();

            // 4. 逐个计算每个资产的发行时点价差
            for (AdurDurationAssetDetailEntity assetDetail : assetDetailList) {
                try {
                    calculateAssetIssueSpread(assetDetail, discountCurveList, discountFactorList);

                    // 更新数据库
                    durationAssetDetailMapper.updateById(assetDetail);
                    successCount++;

                } catch (Exception e) {
                    log.error("计算资产{}的发行时点价差失败", assetDetail.getAssetNumber(), e);
                }
            }

            log.info("发行时点价差计算完成，账期：{}，总数：{}，成功：{}，失败：{}", 
                    accountPeriod, totalCount, successCount, totalCount - successCount);

            return successCount > 0;

        } catch (Exception e) {
            log.error("计算发行时点价差失败，账期：{}", accountPeriod, e);
            return false;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateIssueSpread(String accountPeriod) {
        log.info("开始重新计算发行时点价差，账期：{}", accountPeriod);

        try {
            // 先清空现有的发行时点价差数据
            int clearCount = durationAssetDetailMapper.clearIssueSpreadByAccountPeriod(accountPeriod);
            log.info("清空账期{}的发行时点价差数据，影响行数：{}", accountPeriod, clearCount);

            // 重新计算
            return calculateIssueSpread(accountPeriod);

        } catch (Exception e) {
            log.error("重新计算发行时点价差失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateEvalSpread(String accountPeriod) {
        log.info("开始计算评估时点价差，账期：{}", accountPeriod);

        try {
            // 1. 查询久期资产明细数据
            List<AdurDurationAssetDetailEntity> assetDetailList = durationAssetDetailMapper.selectByAccountPeriod(accountPeriod);
            if (CollectionUtils.isEmpty(assetDetailList)) {
                log.warn("未找到账期{}的久期资产明细数据", accountPeriod);
                return true; // 没有数据也算成功
            }

            // 2. 查询月度折现曲线表含价差数据（曲线细分类=5）
            List<AdurMonthlyDiscountCurveWithSpreadEntity> discountCurveList =
                    monthlyDiscountCurveWithSpreadMapper.selectByAccountPeriod(accountPeriod);
            if (CollectionUtils.isEmpty(discountCurveList)) {
                log.warn("未找到账期{}的月度折现曲线表含价差数据", accountPeriod);
                return false;
            }

            // 3. 查询月度折现因子表含价差数据（用于更新）
            List<AdurMonthlyDiscountFactorWithSpreadEntity> discountFactorList =
                    monthlyDiscountFactorWithSpreadMapper.selectByAccountPeriod(accountPeriod);

            int successCount = 0;
            int totalCount = assetDetailList.size();

            // 4. 逐个计算每个资产的评估时点价差
            for (AdurDurationAssetDetailEntity assetDetail : assetDetailList) {
                try {
                    calculateAssetEvalSpread(assetDetail, discountCurveList, discountFactorList);

                    // 更新数据库
                    durationAssetDetailMapper.updateById(assetDetail);
                    successCount++;

                } catch (Exception e) {
                    log.error("计算资产{}的评估时点价差失败", assetDetail.getAssetNumber(), e);
                }
            }

            log.info("评估时点价差计算完成，账期：{}，总数：{}，成功：{}，失败：{}",
                    accountPeriod, totalCount, successCount, totalCount - successCount);

            return successCount > 0;

        } catch (Exception e) {
            log.error("计算评估时点价差失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateEvalSpread(String accountPeriod) {
        log.info("开始重新计算评估时点价差，账期：{}", accountPeriod);

        try {
            // 先清空现有的评估时点价差数据
            int clearCount = durationAssetDetailMapper.clearEvalSpreadByAccountPeriod(accountPeriod);
            log.info("清空账期{}的评估时点价差数据，影响行数：{}", accountPeriod, clearCount);

            // 重新计算
            return calculateEvalSpread(accountPeriod);

        } catch (Exception e) {
            log.error("重新计算评估时点价差失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    /**
     * 计算单个资产的发行时点价差
     *
     * @param assetDetail 久期资产明细实体
     * @param discountCurveList 月度折现曲线表含价差数据列表
     * @param discountFactorList 月度折现因子表含价差数据列表
     */
    private void calculateAssetIssueSpread(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurMonthlyDiscountCurveWithSpreadEntity> discountCurveList,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> discountFactorList) {

        Integer assetNumber = assetDetail.getAssetNumber();
        String accountName = assetDetail.getAccountName();
        String securityCode = assetDetail.getSecurityCode();

        // 1. 检查折现曲线标识
        if ("0".equals(assetDetail.getCurveId())) {
            // 如果折现曲线标识=0，赋值为0
            assetDetail.setIssueSpread(BigDecimal.ZERO);
            log.debug("资产{}折现曲线标识为0，发行时点价差设为0", assetNumber);
            return;
        }

        // 2. 查找对应的月度折现曲线数据（曲线细分类=1）
        AdurMonthlyDiscountCurveWithSpreadEntity discountCurveEntity = findDiscountCurve(
                discountCurveList, accountName, securityCode, CURVE_SUB_CATEGORY_ISSUE);

        if (discountCurveEntity == null) {
            log.warn("资产{}未找到曲线细分类{}的月度折现曲线数据，发行时点价差设为0",
                    assetNumber, CURVE_SUB_CATEGORY_ISSUE);
            assetDetail.setIssueSpread(BigDecimal.ZERO);
            return;
        }

        // 3. 检查必要的计算参数
        if (assetDetail.getIssuePresentValue() == null || 
            assetDetail.getIssuePresentValue().compareTo(BigDecimal.ZERO) == 0) {
            log.warn("资产{}发行时点资产现值为空或为0，发行时点价差设为0", assetNumber);
            assetDetail.setIssueSpread(BigDecimal.ZERO);
            return;
        }

        if (!StringUtils.hasText(assetDetail.getIssueCashflowSet()) || 
            "{}".equals(assetDetail.getIssueCashflowSet())) {
            log.warn("资产{}发行时点现金流值集为空，发行时点价差设为0", assetNumber);
            assetDetail.setIssueSpread(BigDecimal.ZERO);
            return;
        }

        // 4. 解析发行时点现金流数据（使用专门的现金流解析方法）
        Map<Integer, BigDecimal> issueCashflowMap = TermDataUtil.parseCashflowTermValues(
                assetDetail.getIssueCashflowSet());
        if (CollectionUtils.isEmpty(issueCashflowMap)) {
            log.warn("资产{}发行时点现金流数据解析失败，发行时点价差设为0", assetNumber);
            assetDetail.setIssueSpread(BigDecimal.ZERO);
            return;
        }

        // 5. 使用GoSeek方法计算发行时点价差
        BigDecimal issueSpread = calculateSpreadUsingGoSeek(
                issueCashflowMap, discountCurveEntity, assetDetail.getIssuePresentValue(),
                discountCurveList, discountFactorList);

        assetDetail.setIssueSpread(issueSpread);

        log.debug("资产{}发行时点价差计算完成，价差：{}", assetNumber, issueSpread);
    }

    /**
     * 计算单个资产的评估时点价差
     *
     * @param assetDetail 久期资产明细实体
     * @param discountCurveList 月度折现曲线表含价差数据列表
     * @param discountFactorList 月度折现因子表含价差数据列表
     */
    private void calculateAssetEvalSpread(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurMonthlyDiscountCurveWithSpreadEntity> discountCurveList,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> discountFactorList) {

        Integer assetNumber = assetDetail.getAssetNumber();
        String accountName = assetDetail.getAccountName();
        String securityCode = assetDetail.getSecurityCode();

        // 1. 检查利差久期资产统计标识
        if ("0".equals(assetDetail.getSpreadDurationStatFlag())) {
            // 如果利差久期资产统计标识=0，赋值为0
            assetDetail.setEvalSpread(BigDecimal.ZERO);
            log.debug("资产{}利差久期资产统计标识为0，评估时点价差设为0", assetNumber);
            return;
        }

        // 2. 查找对应的月度折现曲线数据（曲线细分类=5）
        AdurMonthlyDiscountCurveWithSpreadEntity discountCurveEntity = findDiscountCurve(
                discountCurveList, accountName, securityCode, CURVE_SUB_CATEGORY_EVAL);

        if (discountCurveEntity == null) {
            log.warn("资产{}未找到曲线细分类{}的月度折现曲线数据，评估时点价差设为0",
                    assetNumber, CURVE_SUB_CATEGORY_EVAL);
            assetDetail.setEvalSpread(BigDecimal.ZERO);
            return;
        }

        // 3. 检查市值是否存在
        if (assetDetail.getMarketValue() == null || assetDetail.getMarketValue().compareTo(BigDecimal.ZERO) == 0) {
            log.warn("资产{}市值为空或为0，评估时点价差设为0", assetNumber);
            assetDetail.setEvalSpread(BigDecimal.ZERO);
            return;
        }

        // 4. 检查评估时点现金流值集是否存在
        if (!StringUtils.hasText(assetDetail.getEvalCashflowSet()) ||
            "{}".equals(assetDetail.getEvalCashflowSet())) {
            log.warn("资产{}评估时点现金流值集为空，评估时点价差设为0", assetNumber);
            assetDetail.setEvalSpread(BigDecimal.ZERO);
            return;
        }

        // 5. 解析评估时点现金流数据
        Map<Integer, BigDecimal> evalCashflowMap = TermDataUtil.parseCashflowTermValues(
                assetDetail.getEvalCashflowSet());
        if (CollectionUtils.isEmpty(evalCashflowMap)) {
            log.warn("资产{}评估时点现金流数据解析失败，评估时点价差设为0", assetNumber);
            assetDetail.setEvalSpread(BigDecimal.ZERO);
            return;
        }

        // 6. 使用GoSeek方法计算评估时点价差
        BigDecimal evalSpread = calculateEvalSpreadUsingGoSeek(
                evalCashflowMap, discountCurveEntity, assetDetail.getMarketValue(),
                discountCurveList, discountFactorList);

        assetDetail.setEvalSpread(evalSpread);

        log.debug("资产{}评估时点价差计算完成，价差：{}", assetNumber, evalSpread);
    }

    /**
     * 查找月度折现因子数据
     *
     * @param discountFactorList 月度折现因子数据列表
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param curveSubCategory 曲线细分类
     * @return 匹配的月度折现因子实体
     */
    private AdurMonthlyDiscountFactorWithSpreadEntity findDiscountFactor(
            List<AdurMonthlyDiscountFactorWithSpreadEntity> discountFactorList,
            String accountName, String securityCode, String curveSubCategory) {

        for (AdurMonthlyDiscountFactorWithSpreadEntity entity : discountFactorList) {
            if (Objects.equals(entity.getAccountName(), accountName) &&
                Objects.equals(entity.getSecurityCode(), securityCode) &&
                Objects.equals(entity.getCurveSubCategory(), curveSubCategory)) {
                return entity;
            }
        }
        return null;
    }

    /**
     * 查找月度折现曲线数据
     *
     * @param discountCurveList 月度折现曲线数据列表
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param curveSubCategory 曲线细分类
     * @return 匹配的月度折现曲线实体
     */
    private AdurMonthlyDiscountCurveWithSpreadEntity findDiscountCurve(
            List<AdurMonthlyDiscountCurveWithSpreadEntity> discountCurveList,
            String accountName, String securityCode, String curveSubCategory) {

        for (AdurMonthlyDiscountCurveWithSpreadEntity entity : discountCurveList) {
            if (Objects.equals(entity.getAccountName(), accountName) &&
                Objects.equals(entity.getSecurityCode(), securityCode) &&
                Objects.equals(entity.getCurveSubCategory(), curveSubCategory)) {
                return entity;
            }
        }
        return null;
    }

    /**
     * 使用GoSeek方法计算价差
     *
     * 新逻辑：
     * 1. 从月度折现曲线表含价差获取月度折现曲线利率值集
     * 2. 在GoSeek迭代中：月度折现曲线利率值集 + 本次迭代的利差
     * 3. 计算折现因子：1/(1+调整后利率)^(月份/12)
     * 4. 收敛后更新曲线类型2,3,4的数据
     *
     * @param cashflowMap 发行时点现金流Map
     * @param discountCurveEntity 月度折现曲线实体（曲线细分类=1）
     * @param targetValue 发行时点资产现值
     * @param allDiscountCurveList 所有月度折现曲线数据（用于更新）
     * @param allDiscountFactorList 所有月度折现因子数据（用于更新）
     * @return 计算得到的价差
     */
    private BigDecimal calculateSpreadUsingGoSeek(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountCurveWithSpreadEntity discountCurveEntity,
            BigDecimal targetValue,
            List<AdurMonthlyDiscountCurveWithSpreadEntity> allDiscountCurveList,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> allDiscountFactorList) {

        if (cashflowMap == null || cashflowMap.isEmpty() ||
            discountCurveEntity == null || targetValue == null ||
            targetValue.compareTo(BigDecimal.ZERO) == 0) {
            log.debug("GoSeek计算参数不完整，返回0价差");
            return BigDecimal.ZERO;
        }

        // 第一步：从月度折现曲线表含价差获取月度折现曲线利率值集
        Map<Integer, BigDecimal> baseCurveRateMap = TermDataUtil.parseTermValues(
                discountCurveEntity.getMonthlyDiscountRateWithSpreadSet());
        if (CollectionUtils.isEmpty(baseCurveRateMap)) {
            log.warn("月度折现曲线利率值集为空，返回0价差");
            return BigDecimal.ZERO;
        }

        // 简化的二分法搜索
        BigDecimal lowSpread = new BigDecimal("-0.1");  // -10%
        BigDecimal highSpread = new BigDecimal("0.1");  // 10%
        BigDecimal tolerance = new BigDecimal("0.002"); // 容差
        BigDecimal lastSpread = BigDecimal.ZERO; // 记录最后一次计算的利差

        for (int i = 0; i < 100; i++) { // 最大迭代100次
            BigDecimal midSpread = lowSpread.add(highSpread).divide(new BigDecimal("2"), 10, RoundingMode.HALF_UP);
            lastSpread = midSpread; // 记录当前利差
            BigDecimal calculatedValue = calculatePresentValueWithDynamicSpread(cashflowMap, baseCurveRateMap, midSpread);
            BigDecimal diff = calculatedValue.subtract(targetValue);

            // 检查是否达到容差要求
            if (diff.abs().compareTo(tolerance) <= 0) {
                // 第三步：收敛后更新曲线类型1,2,3,4的数据
                updateCurveDataAfterConvergence(discountCurveEntity, midSpread, baseCurveRateMap,
                        allDiscountCurveList, allDiscountFactorList);

                return midSpread.setScale(10, RoundingMode.HALF_UP);
            }

            // 更新搜索范围
            if (diff.compareTo(BigDecimal.ZERO) > 0) {
                lowSpread = midSpread;
            } else {
                highSpread = midSpread;
            }
        }

        // 如果未收敛，返回最后一次计算的利差
        log.warn("GoSeek未收敛，返回最后计算的利差: {}", lastSpread);

        // 即使未收敛，也更新曲线数据，使用最后计算的利差
        updateCurveDataAfterConvergence(discountCurveEntity, lastSpread, baseCurveRateMap,
                allDiscountCurveList, allDiscountFactorList);

        return lastSpread.setScale(10, RoundingMode.HALF_UP);
    }

    /**
     * 使用动态价差计算现值
     *
     * 新逻辑：
     * 1. 月度折现曲线利率值集 + 本次迭代的利差
     * 2. 计算折现因子：1/(1+调整后利率)^(月份/12)
     * 3. 计算现值：现金流 * 折现因子
     *
     * @param cashflowMap 现金流Map
     * @param baseCurveRateMap 基础月度折现曲线利率值集
     * @param spread 价差
     * @return 现值
     */
    private BigDecimal calculatePresentValueWithDynamicSpread(
            Map<Integer, BigDecimal> cashflowMap,
            Map<Integer, BigDecimal> baseCurveRateMap,
            BigDecimal spread) {

        if (CollectionUtils.isEmpty(baseCurveRateMap)) {
            return BigDecimal.ZERO;
        }

        BigDecimal presentValue = BigDecimal.ZERO;

        for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
            Integer termIndex = entry.getKey();
            BigDecimal cashflow = entry.getValue();
            BigDecimal baseRate = baseCurveRateMap.get(termIndex);

            if (cashflow != null && baseRate != null) {
                // 第二步：月度折现曲线利率值集 + 本次goseek迭代的利差
                BigDecimal adjustedRate = baseRate.add(spread);

                // 计算折现因子：1/(1+调整后利率)^(月份/12)
                BigDecimal onePlusRate = BigDecimal.ONE.add(adjustedRate);
                BigDecimal exponent = new BigDecimal(termIndex).divide(new BigDecimal("12"), 15, RoundingMode.HALF_UP);
                BigDecimal powerResult = bigDecimalPow(onePlusRate, exponent, 15);
                BigDecimal factorBD = BigDecimal.ONE.divide(powerResult, 15, RoundingMode.HALF_UP);
                presentValue = presentValue.add(cashflow.multiply(factorBD));
            }
        }

        return presentValue.setScale(10, RoundingMode.HALF_UP);
    }

    /**
     * 高精度BigDecimal幂运算
     * 使用对数和指数函数计算 base^exponent
     *
     * @param base 底数
     * @param exponent 指数
     * @param scale 计算精度
     * @return base^exponent
     */
    private BigDecimal bigDecimalPow(BigDecimal base, BigDecimal exponent, int scale) {
        // 对于简单情况的优化
        if (exponent.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ONE;
        }
        if (exponent.compareTo(BigDecimal.ONE) == 0) {
            return base;
        }
        if (base.compareTo(BigDecimal.ONE) == 0) {
            return BigDecimal.ONE;
        }

        try {
            // 使用对数和指数函数：base^exponent = e^(exponent * ln(base))
            double baseDouble = base.doubleValue();
            double exponentDouble = exponent.doubleValue();

            // 检查是否在合理范围内
            if (baseDouble <= 0) {
                throw new ArithmeticException("底数必须大于0");
            }

            // 使用高精度计算
            double logBase = Math.log(baseDouble);
            double result = Math.exp(exponentDouble * logBase);

            // 转换回BigDecimal并保持精度
            return new BigDecimal(result).setScale(scale, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.warn("高精度幂运算失败，使用标准Math.pow作为备选方案", e);
            // 备选方案：使用标准Math.pow
            double result = Math.pow(base.doubleValue(), exponent.doubleValue());
            return BigDecimal.valueOf(result).setScale(scale, RoundingMode.HALF_UP);
        }
    }

    /**
     * 收敛后更新曲线类型1,2,3,4的数据
     *
     * 计算逻辑：
     * 1. 月度折现曲线利率值集 = 月度折现曲线利率值集 + 本次goseek迭代的利差
     * 2. 月度折现因子表含价差值集 = 1/(1+月度折现曲线表含价差.期限x)^(月份/12)
     *
     * @param originalCurveEntity 原始曲线实体（曲线细分类=1）
     * @param finalSpread 收敛后的最终价差
     * @param baseCurveRateMap 基础曲线利率值集
     * @param allDiscountCurveList 所有月度折现曲线数据
     * @param allDiscountFactorList 所有月度折现因子数据
     */
    private void updateCurveDataAfterConvergence(
            AdurMonthlyDiscountCurveWithSpreadEntity originalCurveEntity,
            BigDecimal finalSpread,
            Map<Integer, BigDecimal> baseCurveRateMap,
            List<AdurMonthlyDiscountCurveWithSpreadEntity> allDiscountCurveList,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> allDiscountFactorList) {

        String accountName = originalCurveEntity.getAccountName();
        String securityCode = originalCurveEntity.getSecurityCode();

        // 更新曲线类型1,2,3,4的数据，每个曲线类型都有自己的基础利率
        String[] curveTypesToUpdate = {"1", "2", "3", "4"};

        for (String curveType : curveTypesToUpdate) {
            // 1. 先找到对应曲线类型的月度折现曲线数据
            AdurMonthlyDiscountCurveWithSpreadEntity targetCurveEntity = null;
            for (AdurMonthlyDiscountCurveWithSpreadEntity curveEntity : allDiscountCurveList) {
                if (Objects.equals(curveEntity.getAccountName(), accountName) &&
                    Objects.equals(curveEntity.getSecurityCode(), securityCode) &&
                    Objects.equals(curveEntity.getCurveSubCategory(), curveType)) {
                    targetCurveEntity = curveEntity;
                    break;
                }
            }

            if (targetCurveEntity == null) {
                log.warn("未找到曲线类型{}的月度折现曲线数据，账户：{}，证券：{}",
                        curveType, accountName, securityCode);
                continue;
            }

            // 2. 获取该曲线类型的基础利率值集
            Map<Integer, BigDecimal> currentBaseCurveRateMap = TermDataUtil.parseTermValues(
                    targetCurveEntity.getMonthlyDiscountRateWithSpreadSet());

            if (CollectionUtils.isEmpty(currentBaseCurveRateMap)) {
                log.warn("曲线类型{}的基础利率值集为空，账户：{}，证券：{}",
                        curveType, accountName, securityCode);
                continue;
            }

            // 3. 计算调整后的曲线利率值集：基础利率 + 收敛后的价差
            Map<Integer, BigDecimal> adjustedCurveRateMap = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : currentBaseCurveRateMap.entrySet()) {
                Integer termIndex = entry.getKey();
                BigDecimal baseRate = entry.getValue();
                BigDecimal adjustedRate = baseRate.add(finalSpread);
                adjustedCurveRateMap.put(termIndex, adjustedRate);
            }

            // 4. 计算调整后的折现因子值集：1/(1+调整后利率)^(月份/12)
            Map<Integer, BigDecimal> adjustedFactorMap = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : adjustedCurveRateMap.entrySet()) {
                Integer termIndex = entry.getKey();
                BigDecimal adjustedRate = entry.getValue();

                // 统一按公式计算：1/(1+调整后利率)^(月份/12)
                double termInYears = (double) termIndex / 12.0;
                double discountFactor = 1.0 / Math.pow(1.0 + adjustedRate.doubleValue(), termInYears);
                adjustedFactorMap.put(termIndex, BigDecimal.valueOf(discountFactor).setScale(10, RoundingMode.HALF_UP));
            }

            // 5. 转换为JSON格式
            String adjustedCurveJson = TermDataUtil.createTermJson(adjustedCurveRateMap);
            String adjustedFactorJson = TermDataUtil.createTermJson(adjustedFactorMap);

            // 6. 更新月度折现曲线数据
            targetCurveEntity.setMonthlyDiscountRateWithSpreadSet(adjustedCurveJson);
            targetCurveEntity.setSpread(finalSpread);
            monthlyDiscountCurveWithSpreadMapper.updateById(targetCurveEntity);
            log.debug("更新曲线类型{}的月度折现曲线数据，账户：{}，证券：{}",
                    curveType, accountName, securityCode);

            // 7. 更新对应的月度折现因子数据
            for (AdurMonthlyDiscountFactorWithSpreadEntity factorEntity : allDiscountFactorList) {
                if (Objects.equals(factorEntity.getAccountName(), accountName) &&
                    Objects.equals(factorEntity.getSecurityCode(), securityCode) &&
                    Objects.equals(factorEntity.getCurveSubCategory(), curveType)) {

                    factorEntity.setMonthlyDiscountFactorSet(adjustedFactorJson);
                    factorEntity.setSpread(finalSpread);
                    monthlyDiscountFactorWithSpreadMapper.updateById(factorEntity);
                    log.debug("更新曲线类型{}的月度折现因子数据，账户：{}，证券：{}",
                            curveType, accountName, securityCode);
                    break; // 找到对应的记录后跳出循环
                }
            }
        }
    }

    /**
     * 使用GoSeek方法计算评估时点价差
     *
     * 计算逻辑：
     * 1. 从月度折现曲线表含价差获取月度折现曲线利率值集（曲线细分类=5）
     * 2. 在GoSeek迭代中：月度折现曲线利率值集 + 本次迭代的利差
     * 3. 计算折现因子：1/(1+调整后利率)^(月份/12)
     * 4. 收敛后更新月度折现曲线表含价差和月度折现因子表含价差的JSON值
     *
     * @param cashflowMap 评估时点现金流Map
     * @param discountCurveEntity 月度折现曲线实体（曲线细分类=5）
     * @param targetValue 市值
     * @param allDiscountCurveList 所有月度折现曲线数据（用于更新）
     * @param allDiscountFactorList 所有月度折现因子数据（用于更新）
     * @return 计算得到的价差
     */
    private BigDecimal calculateEvalSpreadUsingGoSeek(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountCurveWithSpreadEntity discountCurveEntity,
            BigDecimal targetValue,
            List<AdurMonthlyDiscountCurveWithSpreadEntity> allDiscountCurveList,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> allDiscountFactorList) {

        if (cashflowMap == null || cashflowMap.isEmpty() ||
            discountCurveEntity == null || targetValue == null ||
            targetValue.compareTo(BigDecimal.ZERO) == 0) {
            log.debug("评估时点价差GoSeek计算参数不完整，返回0价差");
            return BigDecimal.ZERO;
        }

        // 第一步：从月度折现曲线表含价差获取月度折现曲线利率值集
        Map<Integer, BigDecimal> baseCurveRateMap = TermDataUtil.parseTermValues(
                discountCurveEntity.getMonthlyDiscountRateWithSpreadSet());
        if (CollectionUtils.isEmpty(baseCurveRateMap)) {
            log.warn("月度折现曲线利率值集为空，返回0价差");
            return BigDecimal.ZERO;
        }

        // 简化的二分法搜索
        BigDecimal lowSpread = new BigDecimal("-0.1");  // -10%
        BigDecimal highSpread = new BigDecimal("0.1");  // 10%
        BigDecimal tolerance = new BigDecimal("0.002"); // 容差
        BigDecimal lastSpread = BigDecimal.ZERO; // 记录最后一次计算的利差

        String accountName = discountCurveEntity.getAccountName();
        String securityCode = discountCurveEntity.getSecurityCode();

        for (int i = 0; i < 100; i++) {
            BigDecimal midSpread = lowSpread.add(highSpread).divide(new BigDecimal("2"), 12, RoundingMode.HALF_UP);
            lastSpread = midSpread;

            // 第二步：月度折现曲线利率值集 + 本次goseek迭代的利差
            Map<Integer, BigDecimal> adjustedCurveRateMap = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : baseCurveRateMap.entrySet()) {
                BigDecimal adjustedRate = entry.getValue().add(midSpread);
                adjustedCurveRateMap.put(entry.getKey(), adjustedRate);
            }

            // 第三步：计算折现因子 = 1/(1+调整后利率)^(月份/12)
            Map<Integer, BigDecimal> discountFactorMap = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : adjustedCurveRateMap.entrySet()) {
                Integer termIndex = entry.getKey();
                BigDecimal adjustedRate = entry.getValue();

                if (termIndex == 0) {
                    discountFactorMap.put(termIndex, BigDecimal.ONE);
                } else {
                    // 计算折现因子：1/(1+调整后利率)^(月份/12)
                    BigDecimal monthFraction = new BigDecimal(termIndex).divide(new BigDecimal("12"), 12, RoundingMode.HALF_UP);
                    BigDecimal onePlusRate = BigDecimal.ONE.add(adjustedRate);

                    // 使用Math.pow计算幂次，然后转换为BigDecimal
                    double powerResult = Math.pow(onePlusRate.doubleValue(), monthFraction.doubleValue());
                    BigDecimal discountFactor = BigDecimal.ONE.divide(
                            new BigDecimal(powerResult), 12, RoundingMode.HALF_UP);
                    discountFactorMap.put(termIndex, discountFactor);
                }
            }

            // 计算现值：现金流向量 * 折现因子向量
            BigDecimal calculatedValue = BigDecimal.ZERO;
            for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
                Integer termIndex = entry.getKey();
                BigDecimal cashflow = entry.getValue();
                BigDecimal discountFactor = discountFactorMap.get(termIndex);

                if (cashflow != null && discountFactor != null) {
                    calculatedValue = calculatedValue.add(cashflow.multiply(discountFactor));
                }
            }

            BigDecimal diff = calculatedValue.subtract(targetValue);

            // 检查是否达到容差要求
            if (diff.abs().compareTo(tolerance) <= 0) {
                log.debug("评估时点价差GoSeek收敛成功，迭代{}次，价差：{}，目标值：{}，计算值：{}",
                        i + 1, midSpread, targetValue, calculatedValue);

                // GoSeek收敛完成之后，需要更新最后一次月度折现曲线表含价差的json值
                String adjustedCurveJson = TermDataUtil.createTermJson(adjustedCurveRateMap);
                discountCurveEntity.setMonthlyDiscountRateWithSpreadSet(adjustedCurveJson);
                monthlyDiscountCurveWithSpreadMapper.updateById(discountCurveEntity);

                // 需要更新最后一次月度折现因子表含价差中的折现因子向量json值
                String adjustedFactorJson = TermDataUtil.createTermJson(discountFactorMap);
                updateDiscountFactorForEvalSpread(allDiscountFactorList, accountName, securityCode, adjustedFactorJson);

                return midSpread.setScale(10, RoundingMode.HALF_UP);
            }

            // 更新搜索范围
            if (diff.compareTo(BigDecimal.ZERO) > 0) {
                lowSpread = midSpread;
            } else {
                highSpread = midSpread;
            }
        }

        // 如果未收敛，返回最后的中值，并更新数据
        BigDecimal finalSpread = lastSpread;
        log.warn("评估时点价差GoSeek未完全收敛，返回最后计算的价差：{}", finalSpread);

        // 即使未完全收敛，也要更新最后一次的计算结果
        Map<Integer, BigDecimal> finalAdjustedCurveRateMap = new HashMap<>();
        for (Map.Entry<Integer, BigDecimal> entry : baseCurveRateMap.entrySet()) {
            BigDecimal adjustedRate = entry.getValue().add(finalSpread);
            finalAdjustedCurveRateMap.put(entry.getKey(), adjustedRate);
        }

        Map<Integer, BigDecimal> finalDiscountFactorMap = new HashMap<>();
        for (Map.Entry<Integer, BigDecimal> entry : finalAdjustedCurveRateMap.entrySet()) {
            Integer termIndex = entry.getKey();
            BigDecimal adjustedRate = entry.getValue();

            if (termIndex == 0) {
                finalDiscountFactorMap.put(termIndex, BigDecimal.ONE);
            } else {
                // 计算折现因子：1/(1+调整后利率)^(月份/12)
                BigDecimal monthFraction = new BigDecimal(termIndex).divide(new BigDecimal("12"), 12, RoundingMode.HALF_UP);
                BigDecimal onePlusRate = BigDecimal.ONE.add(adjustedRate);

                // 使用Math.pow计算幂次，然后转换为BigDecimal
                double powerResult = Math.pow(onePlusRate.doubleValue(), monthFraction.doubleValue());
                BigDecimal discountFactor = BigDecimal.ONE.divide(
                        new BigDecimal(powerResult), 12, RoundingMode.HALF_UP);
                finalDiscountFactorMap.put(termIndex, discountFactor);
            }
        }

        String finalCurveJson = TermDataUtil.createTermJson(finalAdjustedCurveRateMap);
        discountCurveEntity.setMonthlyDiscountRateWithSpreadSet(finalCurveJson);
        monthlyDiscountCurveWithSpreadMapper.updateById(discountCurveEntity);

        String finalFactorJson = TermDataUtil.createTermJson(finalDiscountFactorMap);
        updateDiscountFactorForEvalSpread(allDiscountFactorList, accountName, securityCode, finalFactorJson);

        return finalSpread;
    }

    /**
     * 更新评估时点价差计算对应的月度折现因子数据
     *
     * @param allDiscountFactorList 所有月度折现因子数据列表
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param adjustedFactorJson 调整后的折现因子JSON
     */
    private void updateDiscountFactorForEvalSpread(
            List<AdurMonthlyDiscountFactorWithSpreadEntity> allDiscountFactorList,
            String accountName, String securityCode, String adjustedFactorJson) {

        // 查找对应的月度折现因子数据（曲线细分类=5）
        for (AdurMonthlyDiscountFactorWithSpreadEntity factorEntity : allDiscountFactorList) {
            if (Objects.equals(factorEntity.getAccountName(), accountName) &&
                Objects.equals(factorEntity.getSecurityCode(), securityCode) &&
                Objects.equals(factorEntity.getCurveSubCategory(), CURVE_SUB_CATEGORY_EVAL)) {

                factorEntity.setMonthlyDiscountFactorSet(adjustedFactorJson);
                monthlyDiscountFactorWithSpreadMapper.updateById(factorEntity);
                log.debug("更新曲线类型{}的月度折现因子数据，账户：{}，证券：{}",
                        CURVE_SUB_CATEGORY_EVAL, accountName, securityCode);
                break; // 找到对应的记录后跳出循环
            }
        }
    }
}
