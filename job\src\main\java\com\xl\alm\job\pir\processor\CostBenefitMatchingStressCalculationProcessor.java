package com.xl.alm.job.pir.processor;

import com.xl.alm.job.pir.service.CostBenefitMatchingStressCalculationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.PostConstruct;

/**
 * PIR投资收益率预测模块 - 成本收益匹配压力测试计算处理器
 * 
 * 功能说明：
 * 根据设计文档 ### 2.2 PIR投资收益率预测模块业务规则 实现
 * 包含TB0010成本收益匹配压力一明细表和TB0011成本收益匹配压力一汇总表的计算
 * 
 * 计算逻辑：
 * 1. TB0010明细表计算：7种压力测试规则
 *    - 账面价值1（利差久期资产）：基于久期资产明细表的σ=77%计算
 *    - 账面价值2（债券型基金）：基于VaR值计算变动
 *    - 账面价值3（固收资管产品）：基于VaR值比率计算变动
 *    - 账面价值4（五级分类资产）：60%变动率
 *    - 账面价值5（股票基金）：基于VaR值计算变动
 *    - 账面价值6（其他权益类）：15%变动率
 *    - 账面价值7（投资性房地产）：20%变动率
 * 
 * 2. TB0011汇总表计算：汇总TB0010明细表的所有账户数据
 * 
 * 数据来源：
 * - 久期资产明细表（t_adur_duration_asset_detail）
 * - 整体资产明细表（t_ast_asset_detail）
 * - 风险10日VaR值表（t_asm_risk_10day_var）
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class CostBenefitMatchingStressCalculationProcessor implements BasicProcessor {

    @Autowired
    private CostBenefitMatchingStressCalculationService costBenefitMatchingStressCalculationService;

    /**
     * 初始化方法
     */
    /**
     * 用于本地测试的初始化方法
     */
    /*@PostConstruct
    public void init() {
        log.info("PIR投资收益率预测模块 - 成本收益匹配压力测试计算处理器初始化完成");

        // 本地测试时取消注释
         TaskContext tc = new TaskContext();
         // 方式1：直接传入账期
         // tc.setJobParams("202406");

         // 方式2：JSON格式（推荐）
         tc.setJobParams("{\"accountingPeriod\":\"202406\"}");
         try {
             process(tc);
         } catch (Exception e) {
             e.printStackTrace();
         }
    }*/

    /**
     * 处理任务
     *
     * @param context 任务上下文
     * @return 处理结果
     */
    @Override
    public ProcessResult process(TaskContext context) {
        log.info("开始执行PIR投资收益率预测模块 - 成本收益匹配压力测试计算任务");

        try {
            // 获取任务参数
            String jobParams = context.getJobParams();
            log.info("任务参数：{}", jobParams);

            // 解析账期参数
            String accountingPeriod = parseAccountingPeriod(jobParams);
            if (accountingPeriod == null || accountingPeriod.trim().isEmpty()) {
                String errorMsg = "账期参数为空，请检查任务参数配置";
                log.error(errorMsg);
                return new ProcessResult(false, errorMsg);
            }

            log.info("开始计算PIR投资收益率预测模块数据，账期：{}", accountingPeriod);

            // 执行计算
            boolean result = costBenefitMatchingStressCalculationService.calculateCostBenefitMatchingStress(accountingPeriod);

            if (result) {
                String successMsg = String.format("PIR投资收益率预测模块计算成功，账期：%s", accountingPeriod);
                log.info(successMsg);
                return new ProcessResult(true, successMsg);
            } else {
                String errorMsg = String.format("PIR投资收益率预测模块计算失败，账期：%s", accountingPeriod);
                log.error(errorMsg);
                return new ProcessResult(false, errorMsg);
            }

        } catch (Exception e) {
            String errorMsg = String.format("PIR投资收益率预测模块计算异常：%s", e.getMessage());
            log.error(errorMsg, e);
            return new ProcessResult(false, errorMsg);
        }
    }

    /**
     * 解析账期参数
     *
     * @param jobParams 任务参数
     * @return 账期，格式：YYYYMM
     */
    private String parseAccountingPeriod(String jobParams) {
        try {
            if (jobParams == null || jobParams.trim().isEmpty()) {
                return null;
            }

            // 支持直接传入账期格式：202506
            if (jobParams.matches("^\\d{6}$")) {
                return jobParams.trim();
            }

            // 支持JSON格式：{"accountingPeriod":"202506"}
            if (jobParams.contains("accountingPeriod")) {
                String[] parts = jobParams.split(":");
                if (parts.length >= 2) {
                    String period = parts[1].replaceAll("[\"{}\\s]", "");
                    if (period.matches("^\\d{6}$")) {
                        return period;
                    }
                }
            }

            log.warn("无法解析账期参数：{}", jobParams);
            return null;

        } catch (Exception e) {
            log.error("解析账期参数异常：{}，参数：{}", e.getMessage(), jobParams);
            return null;
        }
    }
}
