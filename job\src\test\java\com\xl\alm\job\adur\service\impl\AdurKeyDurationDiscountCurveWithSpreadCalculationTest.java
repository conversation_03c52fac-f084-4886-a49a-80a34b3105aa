package com.xl.alm.job.adur.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xl.alm.job.adur.entity.AdurKeyDurationDiscountCurveWithSpreadEntity;
import com.xl.alm.job.adur.entity.AdurKeyDurationParameterEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity;
import com.xl.alm.job.adur.mapper.AdurKeyDurationDiscountCurveWithSpreadMapper;
import com.xl.alm.job.adur.mapper.AdurKeyDurationParameterMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveWithSpreadMapper;
import com.xl.alm.job.adur.util.TermDataUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ADUR关键久期折现曲线含价差计算逻辑测试类
 * 验证修复后的计算逻辑是否正确
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
@Transactional
public class AdurKeyDurationDiscountCurveWithSpreadCalculationTest {

    @Autowired
    private AdurKeyDurationDiscountCurveWithSpreadServiceImpl service;

    @Autowired
    private AdurKeyDurationParameterMapper keyDurationParameterMapper;

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadMapper monthlyDiscountCurveWithSpreadMapper;

    @Autowired
    private AdurKeyDurationDiscountCurveWithSpreadMapper keyDurationDiscountCurveWithSpreadMapper;

    private String testAccountPeriod = "202406";
    private String testAssetNumber = "TEST001";
    private String testSecurityCode = "000001";
    private String testAccountName = "01"; // 传统账户

    @BeforeEach
    public void setUp() {
        // 清理测试数据
        keyDurationParameterMapper.deleteByAccountPeriod(testAccountPeriod);
        monthlyDiscountCurveWithSpreadMapper.deleteByAccountPeriod(testAccountPeriod);
        keyDurationDiscountCurveWithSpreadMapper.deleteByAccountPeriod(testAccountPeriod);
    }

    @Test
    public void testKeyDurationCurveCalculationLogic() {
        // 1. 准备关键久期参数数据
        prepareKeyDurationParameterData();

        // 2. 准备月度折现曲线含价差数据（曲线细分类=2）
        prepareMonthlyDiscountCurveWithSpreadData();

        // 3. 执行计算
        boolean result = service.calculateKeyDurationDiscountCurveWithSpread(testAccountPeriod);
        assertTrue(result, "计算应该成功");

        // 4. 验证计算结果
        verifyCalculationResults();
    }

    /**
     * 准备关键久期参数数据
     */
    private void prepareKeyDurationParameterData() {
        AdurKeyDurationParameterEntity parameter = new AdurKeyDurationParameterEntity();
        parameter.setAccountPeriod(testAccountPeriod);
        parameter.setKeyDuration("1"); // 关键期限1年

        // 构造参数值集JSON：{"0":{"val":0.001},"1":{"val":0.001},...,"600":{"val":0.001}}
        Map<String, Map<String, Object>> parameterValueSet = new HashMap<>();
        for (int i = 0; i <= 600; i++) {
            Map<String, Object> valueMap = new HashMap<>();
            valueMap.put("val", 0.001); // 固定参数值0.001
            parameterValueSet.put(String.valueOf(i), valueMap);
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String parameterJson = objectMapper.writeValueAsString(parameterValueSet);
            parameter.setParameterValSet(parameterJson);
        } catch (Exception e) {
            parameter.setParameterValSet("{}");
        }

        parameter.setCreateTime(new Date());
        parameter.setCreateBy("TEST");
        parameter.setUpdateTime(new Date());
        parameter.setUpdateBy("TEST");

        keyDurationParameterMapper.batchInsertKeyDurationParameter(Arrays.asList(parameter));
    }

    /**
     * 准备月度折现曲线含价差数据
     */
    private void prepareMonthlyDiscountCurveWithSpreadData() {
        AdurMonthlyDiscountCurveWithSpreadEntity curveEntity = new AdurMonthlyDiscountCurveWithSpreadEntity();
        curveEntity.setAccountPeriod(testAccountPeriod);
        curveEntity.setDurationType("01"); // 修正久期
        curveEntity.setBasisPointType("01"); // 0bp
        curveEntity.setDateType("02"); // 评估时点
        curveEntity.setDate(new Date());
        curveEntity.setSpreadType("01"); // 发行时点价差
        curveEntity.setSpread(new BigDecimal("0.01"));
        curveEntity.setCurveSubCategory("2"); // 曲线细分类=2（关键匹配条件）
        curveEntity.setAssetNumber(Integer.valueOf(testAssetNumber));
        curveEntity.setAccountName(testAccountName);
        curveEntity.setAssetName("测试资产");
        curveEntity.setSecurityCode(testSecurityCode);
        curveEntity.setCurveId("1");

        // 设置月度折现曲线含价差期限数据：基础利率4%
        Map<Integer, BigDecimal> curveTerms = new HashMap<>();
        for (int i = 0; i <= 600; i++) {
            curveTerms.put(i, new BigDecimal("0.04")); // 固定基础利率4%
        }
        curveEntity.setMonthlyDiscountRateWithSpreadSet(TermDataUtil.createTermJson(curveTerms));

        curveEntity.setCreateTime(new Date());
        curveEntity.setCreateBy("TEST");
        curveEntity.setUpdateTime(new Date());
        curveEntity.setUpdateBy("TEST");

        monthlyDiscountCurveWithSpreadMapper.batchInsertMonthlyDiscountCurveWithSpread(Arrays.asList(curveEntity));
    }

    /**
     * 验证计算结果
     */
    private void verifyCalculationResults() {
        List<AdurKeyDurationDiscountCurveWithSpreadEntity> resultList = keyDurationDiscountCurveWithSpreadMapper
                .selectByAccountPeriod(testAccountPeriod);

        // 应该生成2条记录（上升和下降两种压力方向）
        // 计算公式：曲线细分类=2的记录数(1) × 关键期限数(1) × 压力方向数(2) = 2条记录
        assertEquals(2, resultList.size(), "应该生成2条记录（上升和下降）");

        for (AdurKeyDurationDiscountCurveWithSpreadEntity entity : resultList) {
            // 验证基本字段
            assertEquals(testAccountPeriod, entity.getAccountPeriod());
            assertEquals("1", entity.getKeyTerm());
            assertEquals(testAssetNumber, entity.getAssetNumber());
            assertEquals(testAccountName, entity.getAccountName());
            assertEquals(testSecurityCode, entity.getSecurityCode());

            // 验证JSON值集不为空
            assertNotNull(entity.getKeyDurationDiscountCurveWithSpreadSet(), "关键久期折现曲线含价差值集不应为空");
            assertNotEquals("{}", entity.getKeyDurationDiscountCurveWithSpreadSet(), "关键久期折现曲线含价差值集不应为空对象");

            // 解析JSON并验证计算逻辑
            Map<Integer, BigDecimal> termValues = TermDataUtil.parseTermValues(entity.getKeyDurationDiscountCurveWithSpreadSet());
            assertEquals(601, termValues.size(), "应该包含0-600共601个期限");

            // 验证计算逻辑
            String stressDirection = entity.getStressDirection();
            BigDecimal expectedValue;

            if ("01".equals(stressDirection)) { // 上升
                // 期限X = 月度折现曲线表含价差.期限X + 关键久期参数表.参数值集[X]
                // 0.04 + 0.001 = 0.041
                expectedValue = new BigDecimal("0.041000");
            } else { // 下降
                // 期限X = 月度折现曲线表含价差.期限X - 关键久期参数表.参数值集[X]
                // 0.04 - 0.001 = 0.039
                expectedValue = new BigDecimal("0.039000");
            }

            // 验证几个关键期限的计算结果
            for (int i : Arrays.asList(0, 1, 12, 24, 600)) {
                BigDecimal actualValue = termValues.get(i);
                assertNotNull(actualValue, "期限" + i + "的值不应为空");
                assertEquals(expectedValue, actualValue, "期限" + i + "的计算结果不正确，压力方向：" + stressDirection);
            }

            System.out.println("压力方向：" + stressDirection + "，期限0计算结果：" + termValues.get(0) + "，期望值：" + expectedValue);
        }
    }

    @Test
    public void testCalculationWithMissingData() {
        // 只准备关键久期参数数据，不准备月度折现曲线含价差数据
        prepareKeyDurationParameterData();

        // 执行计算
        boolean result = service.calculateKeyDurationDiscountCurveWithSpread(testAccountPeriod);
        assertTrue(result, "即使缺少基础数据，任务也应该成功完成");

        // 验证结果为空
        List<AdurKeyDurationDiscountCurveWithSpreadEntity> resultList = keyDurationDiscountCurveWithSpreadMapper
                .selectByAccountPeriod(testAccountPeriod);
        assertTrue(resultList.isEmpty(), "缺少基础数据时，结果应该为空");
    }

    @Test
    public void testCalculationWithWrongCurveSubCategory() {
        // 准备关键久期参数数据
        prepareKeyDurationParameterData();

        // 准备月度折现曲线含价差数据，但曲线细分类不是2
        AdurMonthlyDiscountCurveWithSpreadEntity curveEntity = new AdurMonthlyDiscountCurveWithSpreadEntity();
        curveEntity.setAccountPeriod(testAccountPeriod);
        curveEntity.setCurveSubCategory("1"); // 曲线细分类=1（不符合匹配条件）
        curveEntity.setAssetNumber(Integer.valueOf(testAssetNumber));
        curveEntity.setAccountName(testAccountName);
        curveEntity.setSecurityCode(testSecurityCode);
        curveEntity.setMonthlyDiscountRateWithSpreadSet("{}");
        curveEntity.setCreateTime(new Date());
        curveEntity.setCreateBy("TEST");

        monthlyDiscountCurveWithSpreadMapper.batchInsertMonthlyDiscountCurveWithSpread(Arrays.asList(curveEntity));

        // 执行计算
        boolean result = service.calculateKeyDurationDiscountCurveWithSpread(testAccountPeriod);
        assertTrue(result, "任务应该成功完成");

        // 验证结果为空（因为没有曲线细分类=2的数据）
        List<AdurKeyDurationDiscountCurveWithSpreadEntity> resultList = keyDurationDiscountCurveWithSpreadMapper
                .selectByAccountPeriod(testAccountPeriod);
        assertTrue(resultList.isEmpty(), "曲线细分类不匹配时，结果应该为空");
    }
}
