package com.xl.alm.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 整体资产明细表实体类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@TableName("t_ast_asset_detail_overall")
public class AssetDetailOverallEntity {

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 账期,格式YYYYMM（如202406） */
    @TableField("accounting_period")
    private String accountingPeriod;

    /** 资产编号 */
    @TableField("asset_number")
    private Integer assetNumber;

    /** 账户名称,如传统账户、分红账户等,通过TB0007账户名称映射表转换 */
    @TableField("account_name")
    private String accountName;

    /** 证券标识代码,来源于TB0006资产定义表 */
    @TableField("security_code")
    private String securityCode;

    /** 资产具体名称,来源于TB0006资产定义表 */
    @TableField("asset_name")
    private String assetName;

    /** 资产小小类,来源于TB0006资产定义表 */
    @TableField("asset_sub_sub_category")
    private String assetSubSubCategory;

    /** 资产大类,通过TB0009资产配置状况分类表匹配 */
    @TableField("asset_major_category")
    private String assetMajorCategory;

    /** 境内外标识,来源于TB0006资产定义表 */
    @TableField("domestic_foreign")
    private String domesticForeign;

    /** 固定收益资产细分,通过TB0008资产基础配置表匹配 */
    @TableField("fixed_income_sub_category")
    private String fixedIncomeSubCategory;

    /** 资产配置状况一级分类 */
    @TableField("asset_allocation_level1")
    private String assetAllocationLevel1;

    /** 资产配置状况二级分类 */
    @TableField("asset_allocation_level2")
    private String assetAllocationLevel2;

    /** 资产配置状况三级分类 */
    @TableField("asset_allocation_level3")
    private String assetAllocationLevel3;

    /** Wind系统中的主体评级,通过TB0005Wind评级表匹配 */
    @TableField("wind_entity_rating")
    private String windEntityRating;

    /** Wind系统中的债项评级,通过TB0005Wind评级表匹配 */
    @TableField("wind_debt_rating")
    private String windDebtRating;

    /** 风控绩效系统主体评级,通过TB0002三账户持仓表匹配 */
    @TableField("risk_entity_rating")
    private String riskEntityRating;

    /** 风控绩效系统债项评级,通过TB0002三账户持仓表匹配 */
    @TableField("risk_debt_rating")
    private String riskDebtRating;

    /** 信用评级维护表评级,直接从TB0006资产定义表的信用评级字段获取 */
    @TableField("credit_rating_maintenance")
    private String creditRatingMaintenance;

    /** 信用评级取值逻辑标识,通过TB0008资产基础配置表匹配 */
    @TableField("credit_rating_logic_flag")
    private String creditRatingLogicFlag;

    /** 最终信用评级,根据信用评级取值逻辑标识计算 */
    @TableField("credit_rating")
    private String creditRating;

    /** 信用评级分类,通过TB0011信用评级映射表匹配 */
    @TableField("credit_rating_category")
    private String creditRatingCategory;

    /** 持仓数量,通过TB0002三账户持仓表匹配 */
    @TableField("holding_quantity")
    private BigDecimal holdingQuantity;

    /** 持仓面值,通过TB0002三账户持仓表匹配 */
    @TableField("holding_face_value")
    private BigDecimal holdingFaceValue;

    /** 成本金额,通过TB0002三账户持仓表匹配 */
    @TableField("cost")
    private BigDecimal cost;

    /** 净价成本,通过TB0002三账户持仓表匹配 */
    @TableField("net_cost")
    private BigDecimal netCost;

    /** 净价市值,通过TB0002三账户持仓表匹配 */
    @TableField("net_market_value")
    private BigDecimal netMarketValue;

    /** 市值,通过TB0001组合持仓表匹配 */
    @TableField("market_value")
    private BigDecimal marketValue;

    /** 1年风险价值,通过TB0003VaR值分析表匹配 */
    @TableField("var_1_year")
    private BigDecimal var1Year;

    /** 3年风险价值,通过TB0003VaR值分析表匹配 */
    @TableField("var_3_year")
    private BigDecimal var3Year;

    /** 账面余额,等于市值 */
    @TableField("book_balance")
    private BigDecimal bookBalance;

    /** 资产减值准备,赋值为0 */
    @TableField("asset_impairment_provision")
    private BigDecimal assetImpairmentProvision;

    /** 账面价值,等于账面余额-资产减值准备 */
    @TableField("book_value")
    private BigDecimal bookValue;

    /** 票面利率,通过TB0002三账户持仓表匹配 */
    @TableField("coupon_rate")
    private BigDecimal couponRate;

    /** 年付息次数,通过TB0002三账户持仓表匹配 */
    @TableField("annual_payment_frequency")
    private Integer annualPaymentFrequency;

    /** 付息方式,通过TB0013付息方式映射表匹配 */
    @TableField("payment_method")
    private String paymentMethod;

    /** 起息日期,通过TB0002三账户持仓表匹配 */
    @TableField("value_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date valueDate;

    /** 买入日期,通过TB0002三账户持仓表匹配 */
    @TableField("purchase_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date purchaseDate;

    /** 到期日期,通过TB0002三账户持仓表匹配 */
    @TableField("maturity_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date maturityDate;

    /** 会计分类类型,通过TB0002三账户持仓表匹配 */
    @TableField("accounting_type")
    private String accountingType;

    /** 剩余期限（年）,计算公式：ROUND((到期日-eomonth(账期转换日期,0))/365,2) */
    @TableField("remaining_term")
    private BigDecimal remainingTerm;

    /** 剩余期限分类标识,剩余期限>=0为0,剩余期限<0为1 */
    @TableField("remaining_term_category")
    private Integer remainingTermCategory;

    /** 剩余期限标识描述,根据资产类型和剩余期限区间计算 */
    @TableField("remaining_term_flag")
    private String remainingTermFlag;

    /** 行业统计标识,先通过TB0008匹配,如为wind中获取则通过TB0004匹配 */
    @TableField("industry_statistics_flag")
    private String industryStatisticsFlag;

    /** 债券类型,政府债券根据资产名称关键字判断,公司债企业债/中期票据根据行业统计标识判断 */
    @TableField("bond_type")
    private String bondType;

    /** 固收资产剩余期限分类,通过TB0015匹配 */
    @TableField("fixed_income_term_category")
    private String fixedIncomeTermCategory;

    /** ALM资产名称,先从TB0006匹配,否则从资产名称中提取 */
    @TableField("alm_asset_name")
    private String almAssetName;

    /** 银行分类,通过TB0012银行分类映射表匹配 */
    @TableField("bank_classification")
    private String bankClassification;

    /** 五级分类,直接从TB0006资产定义表的五级分类字段获取 */
    @TableField("five_level_classification")
    private String fiveLevelClassification;

    /** 可计算现金流标识,如剩余期限<0则为0,否则通过TB0008匹配 */
    @TableField("calculable_cashflow_flag")
    private String calculableCashflowFlag;

    /** 利差久期资产统计标识,通过TB0008资产基础配置表匹配 */
    @TableField("spread_duration_statistics_flag")
    private String spreadDurationStatisticsFlag;

    /** 单一资产统计标识,通过TB0008资产基础配置表匹配 */
    @TableField("single_asset_statistics_flag")
    private String singleAssetStatisticsFlag;

    /** 五级分类资产统计标识,通过TB0008资产基础配置表匹配 */
    @TableField("five_level_statistics_flag")
    private String fiveLevelStatisticsFlag;

    /** 资产流动性分类,政府债券按债券类型+会计分类匹配 */
    @TableField("asset_liquidity_category")
    private String assetLiquidityCategory;

    /** 变现系数,政府债券按债券类型+会计分类匹配 */
    @TableField("realization_coefficient")
    private BigDecimal realizationCoefficient;

    /** 折现曲线使用评级,通过TB0011信用评级映射表匹配 */
    @TableField("discount_curve_rating")
    private String discountCurveRating;

    /** 折现曲线标识,如可计算现金流标识=0则不涉及,否则通过TB0014匹配 */
    @TableField("discount_curve_flag")
    private String discountCurveFlag;

    /** 调整后起息日,计算公式：eomonth(起息日,0) */
    @TableField("adjusted_value_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date adjustedValueDate;

    /** 调整后买入日,计算公式：eomonth(买入日,0) */
    @TableField("adjusted_purchase_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date adjustedPurchaseDate;

    /** 调整后到期日,计算公式：eomonth(到期日,0) */
    @TableField("adjusted_maturity_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date adjustedMaturityDate;

    /** 创建时间 */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建者 */
    @TableField("create_by")
    private String createBy;

    /** 更新时间 */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 更新者 */
    @TableField("update_by")
    private String updateBy;

    /** 是否删除，0:否，1:是 */
    @TableField("is_del")
    private Integer isDel;
}
