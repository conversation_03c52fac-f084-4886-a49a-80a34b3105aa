package com.xl.alm.job.adur.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xl.alm.job.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ADUR久期资产明细表实体类
 * 对应表：t_adur_duration_asset_detail
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_adur_duration_asset_detail")
public class AdurDurationAssetDetailEntity extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId
    @TableField("id")
    private Long id;

    /**
     * 账期
     */
    @TableField("account_period")
    private String accountPeriod;

    /**
     * 资产编号
     */
    @TableField("asset_number")
    private Integer assetNumber;

    /**
     * 账户名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 资产名称
     */
    @TableField("asset_name")
    private String assetName;

    /**
     * 证券代码
     */
    @TableField("security_code")
    private String securityCode;

    /**
     * 资产小小类
     */
    @TableField("asset_sub_category")
    private String assetSubCategory;

    /**
     * 持仓面值
     */
    @TableField("holding_face_value")
    private BigDecimal holdingFaceValue;

    /**
     * 市值
     */
    @TableField("market_value")
    private BigDecimal marketValue;

    /**
     * 账面余额
     */
    @TableField("book_balance")
    private BigDecimal bookBalance;

    /**
     * 账面价值
     */
    @TableField("book_value")
    private BigDecimal bookValue;

    /**
     * 票面利率
     */
    @TableField("coupon_rate")
    private BigDecimal couponRate;

    /**
     * 付息方式
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 折现曲线标识
     */
    @TableField("curve_id")
    private String curveId;

    /**
     * 调整起息日
     */
    @TableField("adjusted_value_date")
    private Date adjustedValueDate;

    /**
     * 调整买入日
     */
    @TableField("adjusted_purchase_date")
    private Date adjustedPurchaseDate;

    /**
     * 调整到期日
     */
    @TableField("adjusted_maturity_date")
    private Date adjustedMaturityDate;

    /**
     * 发行时点价差计算标识
     */
    @TableField("issue_spread_calc_flag")
    private String issueSpreadCalcFlag;

    /**
     * 利差久期资产统计标识
     */
    @TableField("spread_duration_stat_flag")
    private String spreadDurationStatFlag;

    /**
     * 评估时点价差
     */
    @TableField("eval_spread")
    private BigDecimal evalSpread;

    /**
     * 利差久期
     */
    @TableField("spread_duration")
    private BigDecimal spreadDuration;

    /**
     * 账面价值σ9%
     */
    @TableField("book_value_sigma_9")
    private BigDecimal bookValueSigma9;

    /**
     * 账面价值σ17%
     */
    @TableField("book_value_sigma_17")
    private BigDecimal bookValueSigma17;

    /**
     * 账面价值σ77%
     */
    @TableField("book_value_sigma_77")
    private BigDecimal bookValueSigma77;

    /**
     * 发行时点资产现值
     */
    @TableField("issue_present_value")
    private BigDecimal issuePresentValue;

    /**
     * 发行时点价差
     */
    @TableField("issue_spread")
    private BigDecimal issueSpread;

    /**
     * 评估时点资产现值
     */
    @TableField("eval_present_value")
    private BigDecimal evalPresentValue;

    /**
     * 评估时点到期收益率
     */
    @TableField("eval_maturity_yield")
    private BigDecimal evalMaturityYield;

    /**
     * 资产修正久期
     */
    @TableField("asset_modified_duration")
    private BigDecimal assetModifiedDuration;

    /**
     * 评估时点资产现值+50bp
     */
    @TableField("eval_present_value_plus_50bp")
    private BigDecimal evalPresentValuePlus50bp;

    /**
     * 评估时点资产现值-50bp
     */
    @TableField("eval_present_value_minus_50bp")
    private BigDecimal evalPresentValueMinus50bp;

    /**
     * 资产有效久期
     */
    @TableField("asset_effective_duration")
    private BigDecimal assetEffectiveDuration;

    // DV10字段 - 基础情景
    /**
     * DV10_0
     */
    @TableField("dv10_1")
    private BigDecimal dv101;

    /**
     * DV10_0.5
     */
    @TableField("dv10_2")
    private BigDecimal dv102;

    /**
     * DV10_1
     */
    @TableField("dv10_3")
    private BigDecimal dv103;

    /**
     * DV10_2
     */
    @TableField("dv10_4")
    private BigDecimal dv104;

    /**
     * DV10_3
     */
    @TableField("dv10_5")
    private BigDecimal dv105;

    /**
     * DV10_4
     */
    @TableField("dv10_6")
    private BigDecimal dv106;

    /**
     * DV10_5
     */
    @TableField("dv10_7")
    private BigDecimal dv107;

    /**
     * DV10_6
     */
    @TableField("dv10_8")
    private BigDecimal dv108;

    /**
     * DV10_7
     */
    @TableField("dv10_9")
    private BigDecimal dv109;

    /**
     * DV10_8
     */
    @TableField("dv10_10")
    private BigDecimal dv1010;

    /**
     * DV10_10
     */
    @TableField("dv10_11")
    private BigDecimal dv1011;

    /**
     * DV10_12
     */
    @TableField("dv10_12")
    private BigDecimal dv1012;

    /**
     * DV10_15
     */
    @TableField("dv10_13")
    private BigDecimal dv1013;

    /**
     * DV10_20
     */
    @TableField("dv10_14")
    private BigDecimal dv1014;

    /**
     * DV10_25
     */
    @TableField("dv10_15")
    private BigDecimal dv1015;

    /**
     * DV10_30
     */
    @TableField("dv10_16")
    private BigDecimal dv1016;

    /**
     * DV10_35
     */
    @TableField("dv10_17")
    private BigDecimal dv1017;

    /**
     * DV10_40
     */
    @TableField("dv10_18")
    private BigDecimal dv1018;

    /**
     * DV10_45
     */
    @TableField("dv10_19")
    private BigDecimal dv1019;

    /**
     * DV10_50
     */
    @TableField("dv10_20")
    private BigDecimal dv1020;

    // DV10字段 - 上升情景
    /**
     * DV10_0_上升
     */
    @TableField("dv10_1_up")
    private BigDecimal dv101Up;

    /**
     * DV10_0.5_上升
     */
    @TableField("dv10_2_up")
    private BigDecimal dv102Up;

    /**
     * DV10_1_上升
     */
    @TableField("dv10_3_up")
    private BigDecimal dv103Up;

    /**
     * DV10_2_上升
     */
    @TableField("dv10_4_up")
    private BigDecimal dv104Up;

    /**
     * DV10_3_上升
     */
    @TableField("dv10_5_up")
    private BigDecimal dv105Up;

    /**
     * DV10_4_上升
     */
    @TableField("dv10_6_up")
    private BigDecimal dv106Up;

    /**
     * DV10_5_上升
     */
    @TableField("dv10_7_up")
    private BigDecimal dv107Up;

    /**
     * DV10_6_上升
     */
    @TableField("dv10_8_up")
    private BigDecimal dv108Up;

    /**
     * DV10_7_上升
     */
    @TableField("dv10_9_up")
    private BigDecimal dv109Up;

    /**
     * DV10_8_上升
     */
    @TableField("dv10_10_up")
    private BigDecimal dv1010Up;

    /**
     * DV10_10_上升
     */
    @TableField("dv10_11_up")
    private BigDecimal dv1011Up;

    /**
     * DV10_12_上升
     */
    @TableField("dv10_12_up")
    private BigDecimal dv1012Up;

    /**
     * DV10_15_上升
     */
    @TableField("dv10_13_up")
    private BigDecimal dv1013Up;

    /**
     * DV10_20_上升
     */
    @TableField("dv10_14_up")
    private BigDecimal dv1014Up;

    /**
     * DV10_25_上升
     */
    @TableField("dv10_15_up")
    private BigDecimal dv1015Up;

    /**
     * DV10_30_上升
     */
    @TableField("dv10_16_up")
    private BigDecimal dv1016Up;

    /**
     * DV10_35_上升
     */
    @TableField("dv10_17_up")
    private BigDecimal dv1017Up;

    /**
     * DV10_40_上升
     */
    @TableField("dv10_18_up")
    private BigDecimal dv1018Up;

    /**
     * DV10_45_上升
     */
    @TableField("dv10_19_up")
    private BigDecimal dv1019Up;

    /**
     * DV10_50_上升
     */
    @TableField("dv10_20_up")
    private BigDecimal dv1020Up;

    // DV10字段 - 下降情景
    /**
     * DV10_0_下降
     */
    @TableField("dv10_1_down")
    private BigDecimal dv101Down;

    /**
     * DV10_0.5_下降
     */
    @TableField("dv10_2_down")
    private BigDecimal dv102Down;

    /**
     * DV10_1_下降
     */
    @TableField("dv10_3_down")
    private BigDecimal dv103Down;

    /**
     * DV10_2_下降
     */
    @TableField("dv10_4_down")
    private BigDecimal dv104Down;

    /**
     * DV10_3_下降
     */
    @TableField("dv10_5_down")
    private BigDecimal dv105Down;

    /**
     * DV10_4_下降
     */
    @TableField("dv10_6_down")
    private BigDecimal dv106Down;

    /**
     * DV10_5_下降
     */
    @TableField("dv10_7_down")
    private BigDecimal dv107Down;

    /**
     * DV10_6_下降
     */
    @TableField("dv10_8_down")
    private BigDecimal dv108Down;

    /**
     * DV10_7_下降
     */
    @TableField("dv10_9_down")
    private BigDecimal dv109Down;

    /**
     * DV10_8_下降
     */
    @TableField("dv10_10_down")
    private BigDecimal dv1010Down;

    /**
     * DV10_10_下降
     */
    @TableField("dv10_11_down")
    private BigDecimal dv1011Down;

    /**
     * DV10_12_下降
     */
    @TableField("dv10_12_down")
    private BigDecimal dv1012Down;

    /**
     * DV10_15_下降
     */
    @TableField("dv10_13_down")
    private BigDecimal dv1013Down;

    /**
     * DV10_20_下降
     */
    @TableField("dv10_14_down")
    private BigDecimal dv1014Down;

    /**
     * DV10_25_下降
     */
    @TableField("dv10_15_down")
    private BigDecimal dv1015Down;

    /**
     * DV10_30_下降
     */
    @TableField("dv10_16_down")
    private BigDecimal dv1016Down;

    /**
     * DV10_35_下降
     */
    @TableField("dv10_17_down")
    private BigDecimal dv1017Down;

    /**
     * DV10_40_下降
     */
    @TableField("dv10_18_down")
    private BigDecimal dv1018Down;

    /**
     * DV10_45_下降
     */
    @TableField("dv10_19_down")
    private BigDecimal dv1019Down;

    /**
     * DV10_50_下降
     */
    @TableField("dv10_20_down")
    private BigDecimal dv1020Down;

    /**
     * 发行时点现金流值集
     */
    @TableField("issue_cashflow_set")
    private String issueCashflowSet;

    /**
     * 评估时点现金流值集
     */
    @TableField("eval_cashflow_set")
    private String evalCashflowSet;

    /**
     * 本金现金流值集
     * 本金现金流：等于持仓面值，仅在最后一期发生,其余期数都是0，json格式
     */
    @TableField("principal_cashflow_set")
    private String principalCashflowSet;

    /**
     * 利息现金流值集
     * 利息现金流：评估时点现金流值集-本金现金流, json格式
     */
    @TableField("interest_cashflow_set")
    private String interestCashflowSet;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_del")
    private Integer isDel = 0;
}
