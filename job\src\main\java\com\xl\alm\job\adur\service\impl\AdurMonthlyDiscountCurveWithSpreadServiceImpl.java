package com.xl.alm.job.adur.service.impl;

import com.xl.alm.job.adur.constant.AdurConstant;
import com.xl.alm.job.adur.entity.AdurDurationAssetDetailEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity;
import com.xl.alm.job.adur.mapper.AdurDurationAssetDetailMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveWithSpreadMapper;
import com.xl.alm.job.adur.service.AdurMonthlyDiscountCurveWithSpreadService;
import com.xl.alm.job.adur.util.TermDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * ADUR月度折现曲线含价差计算服务实现类
 * 对应用例：UC0006 计算月度折现曲线含价差
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurMonthlyDiscountCurveWithSpreadServiceImpl implements AdurMonthlyDiscountCurveWithSpreadService {

    @Autowired
    private AdurMonthlyDiscountCurveMapper monthlyDiscountCurveMapper;

    @Autowired
    private AdurDurationAssetDetailMapper durationAssetDetailMapper;

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadMapper monthlyDiscountCurveWithSpreadMapper;

    /**
     * 计算月度折现曲线含价差
     *
     * @param accountPeriod 账期，格式YYYYMM
     * @return 处理结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateMonthlyDiscountCurveWithSpread(String accountPeriod) {
        log.info("开始执行月度折现曲线含价差计算，账期：{}", accountPeriod);
        try {
            // 步骤1：加载基础数据
            Map<Integer, Map<String, AdurMonthlyDiscountCurveEntity>> monthlyDiscountCurveMap = loadMonthlyDiscountCurveData(accountPeriod);
            log.info("加载月度折现曲线不含价差数据完成，共加载{}个资产的数据", monthlyDiscountCurveMap.size());

            Map<Integer, AdurDurationAssetDetailEntity> assetDetailMap = loadDurationAssetDetailData(accountPeriod);
            log.info("加载久期资产明细数据完成，共加载{}个资产的数据", assetDetailMap.size());

            // 步骤2：计算含价差折现曲线
            List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveWithSpreadList = calculateMonthlyDiscountCurveWithSpreadData(
                    accountPeriod, monthlyDiscountCurveMap, assetDetailMap);
            log.info("计算月度折现曲线含价差数据完成，共计算{}条记录", monthlyDiscountCurveWithSpreadList.size());

            // 步骤3：数据入表
            if (!CollectionUtils.isEmpty(monthlyDiscountCurveWithSpreadList)) {
                // 先删除原有数据
                monthlyDiscountCurveWithSpreadMapper.deleteByAccountPeriod(accountPeriod);
                log.info("删除账期{}的原有月度折现曲线含价差数据", accountPeriod);

                // 批量插入新数据
                int insertCount = monthlyDiscountCurveWithSpreadMapper.batchInsertMonthlyDiscountCurveWithSpread(monthlyDiscountCurveWithSpreadList);
                log.info("批量插入月度折现曲线含价差数据完成，插入{}条记录", insertCount);
            }

            log.info("月度折现曲线含价差计算完成，账期：{}", accountPeriod);
            return true;

        } catch (Exception e) {
            log.error("月度折现曲线含价差计算失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    /**
     * 加载月度折现曲线不含价差数据
     *
     * @param accountPeriod 账期
     * @return 月度折现曲线数据Map，key为资产编号，value为日期类型Map
     */
    private Map<Integer, Map<String, AdurMonthlyDiscountCurveEntity>> loadMonthlyDiscountCurveData(String accountPeriod) {
        List<AdurMonthlyDiscountCurveEntity> monthlyDiscountCurveList = monthlyDiscountCurveMapper.selectByAccountPeriod(accountPeriod);

        Map<Integer, Map<String, AdurMonthlyDiscountCurveEntity>> monthlyDiscountCurveMap = new HashMap<>();

        for (AdurMonthlyDiscountCurveEntity entity : monthlyDiscountCurveList) {
            Integer assetNumber = entity.getAssetNumber();
            String dateType = entity.getDateType();

            monthlyDiscountCurveMap.computeIfAbsent(assetNumber, k -> new HashMap<>()).put(dateType, entity);
        }

        return monthlyDiscountCurveMap;
    }

    /**
     * 加载久期资产明细数据
     *
     * @param accountPeriod 账期
     * @return 久期资产明细数据Map，key为资产编号
     */
    private Map<Integer, AdurDurationAssetDetailEntity> loadDurationAssetDetailData(String accountPeriod) {
        List<AdurDurationAssetDetailEntity> assetDetailList = durationAssetDetailMapper.selectByAccountPeriod(accountPeriod);

        Map<Integer, AdurDurationAssetDetailEntity> assetDetailMap = new HashMap<>();

        for (AdurDurationAssetDetailEntity entity : assetDetailList) {
            assetDetailMap.put(entity.getAssetNumber(), entity);
        }

        return assetDetailMap;
    }

    /**
     * 计算月度折现曲线含价差数据
     *
     * @param accountPeriod           账期
     * @param monthlyDiscountCurveMap 月度折现曲线不含价差数据Map
     * @param assetDetailMap          久期资产明细数据Map
     * @return 月度折现曲线含价差列表
     */
    private List<AdurMonthlyDiscountCurveWithSpreadEntity> calculateMonthlyDiscountCurveWithSpreadData(
            String accountPeriod,
            Map<Integer, Map<String, AdurMonthlyDiscountCurveEntity>> monthlyDiscountCurveMap,
            Map<Integer, AdurDurationAssetDetailEntity> assetDetailMap) {

        List<AdurMonthlyDiscountCurveWithSpreadEntity> resultList = new ArrayList<>();

        // 遍历每个资产
        for (Map.Entry<Integer, Map<String, AdurMonthlyDiscountCurveEntity>> assetEntry : monthlyDiscountCurveMap.entrySet()) {
            Integer assetNumber = assetEntry.getKey();
            Map<String, AdurMonthlyDiscountCurveEntity> dateTypeMap = assetEntry.getValue();

            // 获取资产明细信息
            AdurDurationAssetDetailEntity assetDetail = assetDetailMap.get(assetNumber);
            if (assetDetail == null) {
                log.warn("未找到资产{}的明细信息，跳过处理", assetNumber);
                continue;
            }

            // 生成5种曲线细分类的数据
            resultList.addAll(generateCurveSubCategories(accountPeriod, dateTypeMap, assetDetail));
        }

        return resultList;
    }

    /**
     * 生成5种曲线细分类的数据
     *
     * @param accountPeriod 账期
     * @param dateTypeMap   日期类型Map
     * @param assetDetail   资产明细
     * @return 曲线细分类数据列表
     */
    private List<AdurMonthlyDiscountCurveWithSpreadEntity> generateCurveSubCategories(
            String accountPeriod,
            Map<String, AdurMonthlyDiscountCurveEntity> dateTypeMap,
            AdurDurationAssetDetailEntity assetDetail) {

        List<AdurMonthlyDiscountCurveWithSpreadEntity> resultList = new ArrayList<>();

        // 曲线细分类=1：修正久期 and 0bp and 发行时点 and 发行时点价差
        AdurMonthlyDiscountCurveEntity issuePointCurve = dateTypeMap.get(AdurConstant.DATE_TYPE_ISSUE);
        if (issuePointCurve != null) {
            AdurMonthlyDiscountCurveWithSpreadEntity category1 = createCurveWithSpreadEntity(
                    accountPeriod, issuePointCurve, assetDetail, "1",
                    AdurConstant.DURATION_TYPE_MODIFIED, AdurConstant.BASIS_POINT_TYPE_ZERO, AdurConstant.DATE_TYPE_ISSUE, AdurConstant.SPREAD_TYPE_ISSUE);
            calculateTermsWithSpread(category1, issuePointCurve, assetDetail.getIssueSpread(), BigDecimal.ZERO);
            resultList.add(category1);
        }

        // 曲线细分类=2：修正久期 and 0bp and 评估时点 and 发行时点价差
        AdurMonthlyDiscountCurveEntity evaluationPointCurve = dateTypeMap.get(AdurConstant.DATE_TYPE_EVALUATION);
        if (evaluationPointCurve != null) {
            AdurMonthlyDiscountCurveWithSpreadEntity category2 = createCurveWithSpreadEntity(
                    accountPeriod, evaluationPointCurve, assetDetail, "2",
                    AdurConstant.DURATION_TYPE_MODIFIED, AdurConstant.BASIS_POINT_TYPE_ZERO, AdurConstant.DATE_TYPE_EVALUATION, AdurConstant.SPREAD_TYPE_ISSUE);
            calculateTermsWithSpread(category2, evaluationPointCurve, assetDetail.getIssueSpread(), BigDecimal.ZERO);
            resultList.add(category2);
        }

        // 曲线细分类=3：有效久期 and +50bp and 评估时点 and 发行时点价差
        if (evaluationPointCurve != null) {
            AdurMonthlyDiscountCurveWithSpreadEntity category3 = createCurveWithSpreadEntity(
                    accountPeriod, evaluationPointCurve, assetDetail, "3",
                    AdurConstant.DURATION_TYPE_EFFECTIVE, AdurConstant.BASIS_POINT_TYPE_PLUS_50, AdurConstant.DATE_TYPE_EVALUATION, AdurConstant.SPREAD_TYPE_ISSUE);
            calculateTermsWithSpread(category3, evaluationPointCurve, assetDetail.getIssueSpread(), new BigDecimal("0.005"));
            resultList.add(category3);
        }

        // 曲线细分类=4：有效久期 and -50bp and 评估时点 and 发行时点价差
        if (evaluationPointCurve != null) {
            AdurMonthlyDiscountCurveWithSpreadEntity category4 = createCurveWithSpreadEntity(
                    accountPeriod, evaluationPointCurve, assetDetail, "4",
                    AdurConstant.DURATION_TYPE_EFFECTIVE, AdurConstant.BASIS_POINT_TYPE_MINUS_50, AdurConstant.DATE_TYPE_EVALUATION, AdurConstant.SPREAD_TYPE_ISSUE);
            calculateTermsWithSpread(category4, evaluationPointCurve, assetDetail.getIssueSpread(), new BigDecimal("-0.005"));
            resultList.add(category4);
        }

        // 曲线细分类=5：利差久期 and 0bp and 评估时点 and 评估时点价差
        if (evaluationPointCurve != null) {
            AdurMonthlyDiscountCurveWithSpreadEntity category5 = createCurveWithSpreadEntity(
                    accountPeriod, evaluationPointCurve, assetDetail, "5",
                    AdurConstant.DURATION_TYPE_SPREAD, AdurConstant.BASIS_POINT_TYPE_ZERO, AdurConstant.DATE_TYPE_EVALUATION, AdurConstant.SPREAD_TYPE_EVALUATION);
            calculateTermsWithSpread(category5, evaluationPointCurve, assetDetail.getEvalSpread(), BigDecimal.ZERO);
            resultList.add(category5);
        }

        return resultList;
    }

    /**
     * 创建月度折现曲线含价差实体
     *
     * @param accountPeriod    账期
     * @param baseCurve        基础曲线
     * @param assetDetail      资产明细
     * @param curveSubCategory 曲线细分类
     * @param durationType     久期类型
     * @param basisPointType   基点类型
     * @param dateType         日期类型
     * @param spreadType       价差类型
     * @return 月度折现曲线含价差实体
     */
    private AdurMonthlyDiscountCurveWithSpreadEntity createCurveWithSpreadEntity(
            String accountPeriod,
            AdurMonthlyDiscountCurveEntity baseCurve,
            AdurDurationAssetDetailEntity assetDetail,
            String curveSubCategory,
            String durationType,
            String basisPointType,
            String dateType,
            String spreadType) {

        AdurMonthlyDiscountCurveWithSpreadEntity entity = new AdurMonthlyDiscountCurveWithSpreadEntity();

        // 设置基本信息
        entity.setAccountPeriod(accountPeriod);
        entity.setDurationType(durationType);
        entity.setBasisPointType(basisPointType);
        entity.setDateType(dateType);
        entity.setDate(baseCurve.getDate());
        entity.setSpreadType(spreadType);
        entity.setCurveSubCategory(curveSubCategory);
        entity.setAssetNumber(assetDetail.getAssetNumber());
        entity.setAccountName(assetDetail.getAccountName());
        entity.setAssetName(assetDetail.getAssetName());
        entity.setSecurityCode(assetDetail.getSecurityCode());
        entity.setCurveId(assetDetail.getCurveId());

        // 设置价差值
        if (AdurConstant.SPREAD_TYPE_ISSUE.equals(spreadType)) {
            entity.setSpread(assetDetail.getIssueSpread());
        } else if (AdurConstant.SPREAD_TYPE_EVALUATION.equals(spreadType)) {
            entity.setSpread(assetDetail.getEvalSpread());
        }

        // 设置审计字段
        entity.setCreateTime(new Date());
        entity.setCreateBy("SYSTEM");
        entity.setUpdateTime(new Date());
        entity.setUpdateBy("SYSTEM");

        return entity;
    }

    /**
     * 计算含价差的期限收益率
     *
     * @param targetEntity         目标实体
     * @param baseCurve            基础曲线
     * @param spread               价差
     * @param basisPointAdjustment 基点调整（+50bp或-50bp）
     */
    private void calculateTermsWithSpread(
            AdurMonthlyDiscountCurveWithSpreadEntity targetEntity,
            AdurMonthlyDiscountCurveEntity baseCurve,
            BigDecimal spread,
            BigDecimal basisPointAdjustment) {

        // 获取基础曲线的期限数据
        Map<Integer, BigDecimal> baseTermValues = TermDataUtil.parseTermValues(baseCurve.getMonthlyDiscountRateSet());
        Map<Integer, BigDecimal> adjustedTermValues = new HashMap<>();

        // 计算0-600个月的含价差收益率
        for (int termIndex = 0; termIndex <= 600; termIndex++) {
            BigDecimal baseRate = baseTermValues.get(termIndex);
            if (baseRate != null) {
                // 计算含价差收益率：基础收益率 + 价差 + 基点调整
                BigDecimal adjustedRate = baseRate;

                if (spread != null) {
                    adjustedRate = adjustedRate.add(spread);
                }

                if (basisPointAdjustment != null && basisPointAdjustment.compareTo(BigDecimal.ZERO) != 0) {
                    adjustedRate = adjustedRate.add(basisPointAdjustment);
                }

                // 保留10位小数
                adjustedTermValues.put(termIndex, adjustedRate.setScale(10, RoundingMode.HALF_UP));
            }
        }

        // 将计算结果转换为JSON格式并设置到目标实体
        String adjustedTermJson = TermDataUtil.createTermJson(adjustedTermValues);
        targetEntity.setMonthlyDiscountRateWithSpreadSet(adjustedTermJson);
    }
}
