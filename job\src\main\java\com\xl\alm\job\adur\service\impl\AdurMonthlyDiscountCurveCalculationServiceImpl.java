package com.xl.alm.job.adur.service.impl;

import com.xl.alm.job.adur.constant.AdurConstant;
import com.xl.alm.job.adur.entity.AdurAnnualDiscountCurveEntity;
import com.xl.alm.job.adur.entity.AdurDurationAssetDetailEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveEntity;
import com.xl.alm.job.adur.mapper.AdurAnnualDiscountCurveMapper;
import com.xl.alm.job.adur.mapper.AdurDurationAssetDetailMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveMapper;
import com.xl.alm.job.adur.service.AdurMonthlyDiscountCurveCalculationService;
import com.xl.alm.job.adur.util.TermDataUtil;
import com.xl.alm.job.adur.util.TermFieldUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * ADUR月度折现曲线不含价差计算服务实现类
 * 对应用例：UC0005 计算月度折现曲线不含价差
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurMonthlyDiscountCurveCalculationServiceImpl implements AdurMonthlyDiscountCurveCalculationService {

    @Autowired
    private AdurAnnualDiscountCurveMapper annualDiscountCurveMapper;

    @Autowired
    private AdurDurationAssetDetailMapper durationAssetDetailMapper;

    @Autowired
    private AdurMonthlyDiscountCurveMapper monthlyDiscountCurveMapper;

    /**
     * 计算月度折现曲线不含价差
     *
     * @param accountPeriod 账期，格式YYYYMM
     * @return 处理结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateMonthlyDiscountCurve(String accountPeriod) {
        log.info("开始执行月度折现曲线不含价差计算，账期：{}", accountPeriod);
        try {
            // 步骤1：加载年度折现曲线数据
            Map<Integer, Map<String, AdurAnnualDiscountCurveEntity>> annualCurveMap = loadAnnualDiscountCurveData(accountPeriod);
            log.info("加载年度折现曲线数据完成，共加载{}个资产的数据", annualCurveMap.size());

            // 加载久期资产明细数据（用于获取到期收益率）
            Map<Integer, AdurDurationAssetDetailEntity> assetDetailMap = loadDurationAssetDetailData(accountPeriod);
            log.info("加载久期资产明细数据完成，共加载{}个资产的数据", assetDetailMap.size());

            // 步骤2：计算月度折现曲线
            List<AdurMonthlyDiscountCurveEntity> monthlyDiscountCurveList = calculateMonthlyDiscountCurveData(
                    accountPeriod, annualCurveMap, assetDetailMap);
            log.info("计算月度折现曲线数据完成，共计算{}条记录", monthlyDiscountCurveList.size());

            // 步骤3：数据入表
            if (!CollectionUtils.isEmpty(monthlyDiscountCurveList)) {
                // 先删除原有数据
                monthlyDiscountCurveMapper.deleteByAccountPeriod(accountPeriod);
                log.info("删除账期{}的原有月度折现曲线数据", accountPeriod);

                // 批量插入新数据
                int insertCount = monthlyDiscountCurveMapper.batchInsertMonthlyDiscountCurve(monthlyDiscountCurveList);
                log.info("批量插入月度折现曲线数据完成，插入{}条记录", insertCount);
            }

            log.info("月度折现曲线不含价差计算完成，账期：{}", accountPeriod);
            return true;

        } catch (Exception e) {
            log.error("月度折现曲线不含价差计算失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    /**
     * 加载年度折现曲线数据
     *
     * @param accountPeriod 账期
     * @return 年度折现曲线数据Map，key为资产编号，value为日期类型Map
     */
    private Map<Integer, Map<String, AdurAnnualDiscountCurveEntity>> loadAnnualDiscountCurveData(String accountPeriod) {
        List<AdurAnnualDiscountCurveEntity> annualCurveList = annualDiscountCurveMapper.selectByAccountPeriod(accountPeriod);
        Map<Integer, Map<String, AdurAnnualDiscountCurveEntity>> annualCurveMap = new HashMap<>();
        
        for (AdurAnnualDiscountCurveEntity entity : annualCurveList) {
            Integer assetNumber = entity.getAssetNumber();
            String dateType = entity.getDateType();
            
            annualCurveMap.computeIfAbsent(assetNumber, k -> new HashMap<>()).put(dateType, entity);
        }
        
        return annualCurveMap;
    }

    /**
     * 加载久期资产明细数据
     *
     * @param accountPeriod 账期
     * @return 久期资产明细数据Map，key为资产编号
     */
    private Map<Integer, AdurDurationAssetDetailEntity> loadDurationAssetDetailData(String accountPeriod) {
        List<AdurDurationAssetDetailEntity> assetDetailList = durationAssetDetailMapper.selectByAccountPeriod(accountPeriod);
        Map<Integer, AdurDurationAssetDetailEntity> assetDetailMap = new HashMap<>();

        for (AdurDurationAssetDetailEntity entity : assetDetailList) {
            assetDetailMap.put(entity.getAssetNumber(), entity);
        }
        
        return assetDetailMap;
    }

    /**
     * 计算月度折现曲线数据
     *
     * @param accountPeriod 账期
     * @param annualCurveMap 年度折现曲线数据Map
     * @param assetDetailMap 久期资产明细数据Map
     * @return 月度折现曲线列表
     */
    private List<AdurMonthlyDiscountCurveEntity> calculateMonthlyDiscountCurveData(
            String accountPeriod,
            Map<Integer, Map<String, AdurAnnualDiscountCurveEntity>> annualCurveMap,
            Map<Integer, AdurDurationAssetDetailEntity> assetDetailMap) {
        
        List<AdurMonthlyDiscountCurveEntity> monthlyDiscountCurveList = new ArrayList<>();
        
        // 遍历每个资产的年度折现曲线数据
        for (Map.Entry<Integer, Map<String, AdurAnnualDiscountCurveEntity>> assetEntry : annualCurveMap.entrySet()) {
            Integer assetNumber = assetEntry.getKey();
            Map<String, AdurAnnualDiscountCurveEntity> dateTypeMap = assetEntry.getValue();
            
            // 获取资产明细信息
            AdurDurationAssetDetailEntity assetDetail = assetDetailMap.get(assetNumber);
            if (assetDetail == null) {
                log.warn("未找到资产{}的明细信息，跳过处理", assetNumber);
                continue;
            }
            
            // 遍历每个日期类型
            for (Map.Entry<String, AdurAnnualDiscountCurveEntity> dateTypeEntry : dateTypeMap.entrySet()) {
                String dateType = dateTypeEntry.getKey();
                AdurAnnualDiscountCurveEntity annualCurve = dateTypeEntry.getValue();
                
                // 创建月度折现曲线实体
                AdurMonthlyDiscountCurveEntity monthlyEntity = createMonthlyDiscountCurveEntity(
                        accountPeriod, annualCurve, assetDetail, dateType);
                
                // 计算0-600个月的收益率
                calculateMonthlyRates(monthlyEntity, annualCurve, assetDetail, dateType);
                
                monthlyDiscountCurveList.add(monthlyEntity);
            }
        }
        
        return monthlyDiscountCurveList;
    }

    /**
     * 创建月度折现曲线实体
     *
     * @param accountPeriod 账期
     * @param annualCurve 年度折现曲线实体
     * @param assetDetail 资产明细实体
     * @param dateType 日期类型
     * @return 月度折现曲线实体
     */
    private AdurMonthlyDiscountCurveEntity createMonthlyDiscountCurveEntity(
            String accountPeriod,
            AdurAnnualDiscountCurveEntity annualCurve,
            AdurDurationAssetDetailEntity assetDetail,
            String dateType) {
        
        AdurMonthlyDiscountCurveEntity monthlyEntity = new AdurMonthlyDiscountCurveEntity();
        monthlyEntity.setAccountPeriod(accountPeriod);
        monthlyEntity.setDateType(dateType);
        monthlyEntity.setDate(annualCurve.getDate());
        monthlyEntity.setAssetNumber(assetDetail.getAssetNumber());
        monthlyEntity.setAccountName(assetDetail.getAccountName());
        monthlyEntity.setAssetName(assetDetail.getAssetName());
        monthlyEntity.setSecurityCode(assetDetail.getSecurityCode());
        monthlyEntity.setCurveId(assetDetail.getCurveId());
        monthlyEntity.setCreateBy("ADUR_UC0005");
        
        return monthlyEntity;
    }

    /**
     * 计算月度收益率
     *
     * @param monthlyEntity 月度折现曲线实体
     * @param annualCurve 年度折现曲线实体
     * @param assetDetail 资产明细实体
     * @param dateType 日期类型
     */
    private void calculateMonthlyRates(
            AdurMonthlyDiscountCurveEntity monthlyEntity,
            AdurAnnualDiscountCurveEntity annualCurve,
            AdurDurationAssetDetailEntity assetDetail,
            String dateType) {

        String curveId = assetDetail.getCurveId();

        // 存储计算结果的Map
        Map<Integer, BigDecimal> termValues = new TreeMap<>(); // 使用TreeMap确保排序

        // 计算0-600个月的收益率
        for (int month = 0; month <= 600; month++) {
            BigDecimal rate = calculateMonthlyRate(month, curveId, dateType, annualCurve, assetDetail);
            termValues.put(month, rate);
        }

        // 将计算结果转换为JSON格式并设置到实体
        String jsonData = TermDataUtil.createTermJson(termValues);
        monthlyEntity.setMonthlyDiscountRateSet(jsonData);
    }

    /**
     * 计算指定月份的收益率
     *
     * @param month 月份(0-600)
     * @param curveId 折现曲线标识
     * @param dateType 日期类型
     * @param annualCurve 年度折现曲线实体
     * @param assetDetail 资产明细实体
     * @return 收益率
     */
    private BigDecimal calculateMonthlyRate(
            int month,
            String curveId,
            String dateType,
            AdurAnnualDiscountCurveEntity annualCurve,
            AdurDurationAssetDetailEntity assetDetail) {
        
        // 规则a: 如果折现曲线标识=0 and 日期类型=发行时点：赋值为"-"
        if ("0".equals(curveId) && AdurConstant.DATE_TYPE_ISSUE.equals(dateType)) {
            return new BigDecimal("0"); // 用-1表示"-"
        }

        // 规则b: 如果折现曲线标识=0 and 日期类型=评估时点：等于久期资产明细表.到期收益率
        if ("0".equals(curveId) && AdurConstant.DATE_TYPE_EVALUATION.equals(dateType)) {
            return assetDetail.getEvalMaturityYield() != null ? assetDetail.getEvalMaturityYield() : BigDecimal.ZERO;
        }
        
        // 规则c: 如果折现曲线标识<>0
        if (!"0".equals(curveId)) {
            // 月份为12的整数倍时：直接取年度折现曲线表中对应年份的收益率
            if (month % 12 == 0) {
                int year = month / 12;
                return getAnnualRate(annualCurve, year);
            } else {
                // 月份不为12的整数倍时：取临近两个整数年的收益率进行线性插值
                int lowerYear = month / 12;
                int upperYear = lowerYear + 1;
                
                BigDecimal lowerRate = getAnnualRate(annualCurve, lowerYear);
                BigDecimal upperRate = getAnnualRate(annualCurve, upperYear);
                
                // 线性插值计算
                double monthInYear = month % 12;
                double weight = monthInYear / 12.0;
                
                BigDecimal interpolatedRate = lowerRate.add(
                        upperRate.subtract(lowerRate).multiply(BigDecimal.valueOf(weight))
                );
                
                return interpolatedRate.setScale(10, RoundingMode.HALF_UP);
            }
        }
        
        return BigDecimal.ZERO;
    }

    /**
     * 获取年度收益率
     *
     * @param annualCurve 年度折现曲线实体
     * @param year 年份(0-50)
     * @return 年度收益率
     */
    private BigDecimal getAnnualRate(AdurAnnualDiscountCurveEntity annualCurve, int year) {
        // 使用TermFieldUtil工具类获取对应年份的收益率
        return TermFieldUtil.getTermValue(annualCurve, year);
    }

    /**
     * 验证月度折现曲线计算结果
     *
     * @param monthlyDiscountCurve 月度折现曲线实体
     * @return 验证结果
     */
    public boolean validateCalculationResult(AdurMonthlyDiscountCurveEntity monthlyDiscountCurve) {
        boolean isValid = true;
        int invalidCount = 0;

        for (int i = 0; i <= 600; i++) {
            BigDecimal value = TermFieldUtil.getTermValue(monthlyDiscountCurve, i);
            if (value == null) {
                invalidCount++;
                isValid = false;
            }
        }

        if (!isValid) {
            log.warn("月度折现曲线计算结果验证失败: assetNumber={}, 无效字段数量={}",
                    monthlyDiscountCurve.getAssetNumber(), invalidCount);
        }

        return isValid;
    }
}
