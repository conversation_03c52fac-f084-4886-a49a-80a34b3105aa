-- =============================================
-- 资产宽表模块数据表DDL
-- 数据库：MySQL 8.0
-- 字符集：utf8
-- =============================================

-- TB0001 - 组合持仓表
DROP TABLE IF EXISTS `t_ast_account_holding`;
CREATE TABLE `t_ast_account_holding` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称,如传统、分红、万能、投连等原始文本,同时作为顶层分类使用',
  `security_code` varchar(20) NOT NULL COMMENT '证券标识代码,如0003956CK、173996等',
  `asset_name` varchar(200) NOT NULL COMMENT '资产名称,如中国农业银行股份有限公司、23重庆04等',
  `market_value` decimal(28,10) DEFAULT 0 COMMENT '市值,如********.44、*********.6等',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_holding` (`accounting_period`, `account_name`, `security_code`, `asset_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0001-组合持仓表';

-- TB0002 - 三账户持仓表
DROP TABLE IF EXISTS `t_ast_three_account_holding`;
CREATE TABLE `t_ast_three_account_holding` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '维度名称1,账户名称,如传统、分红等',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码,如0003956CK、173996等',
  `security_name` varchar(200) DEFAULT NULL COMMENT '证券简称,如中国农业银行股份有限公司、23重庆04等',
  `dimension_name_2` varchar(100) DEFAULT NULL COMMENT '维度名称2,如固定收益类投资资产',
  `dimension_name_3` varchar(100) DEFAULT NULL COMMENT '维度名称3,如境内资产',
  `dimension_name_4` varchar(100) DEFAULT NULL COMMENT '维度名称4,如传统固定收益类投资资产',
  `dimension_name_5` varchar(100) DEFAULT NULL COMMENT '维度名称5,如存款、政府债券',
  `dimension_name_6` varchar(100) DEFAULT NULL COMMENT '维度名称6,如具体分类标识',
  `holding_quantity` decimal(28,10) DEFAULT 0 COMMENT '持仓数量',
  `holding_face_value` decimal(28,10) DEFAULT 0 COMMENT '持仓面值',
  `cost` decimal(28,10) DEFAULT 0 COMMENT '成本',
  `net_cost` decimal(28,10) DEFAULT 0 COMMENT '净价成本',
  `net_market_value` decimal(28,10) DEFAULT 0 COMMENT '净价市值',
  `market_value` decimal(28,10) DEFAULT 0 COMMENT '市值',
  `coupon_rate` decimal(10,6) DEFAULT 0 COMMENT '票面利率',
  `annual_payment_frequency` int(5) DEFAULT 0 COMMENT '年付息次数',
  `value_date` date DEFAULT NULL COMMENT '起息日',
  `maturity_date` date DEFAULT NULL COMMENT '到期日',
  `accounting_type` varchar(50) DEFAULT NULL COMMENT '会计类型,如可供出售类、其他类',
  `latest_purchase_date` date DEFAULT NULL COMMENT '最新买入日期',
  `entity_rating` varchar(10) DEFAULT NULL COMMENT '主体外部评级',
  `security_rating` varchar(10) DEFAULT NULL COMMENT '证券外部评级',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_three_account_holding` (`accounting_period`, `account_name`, `security_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0002-三账户持仓表';

-- TB0003 - VaR值分析表
DROP TABLE IF EXISTS `t_ast_var_analysis`;
CREATE TABLE `t_ast_var_analysis` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `data_type` varchar(20) NOT NULL COMMENT '数据类型,债基1年、债基3年、权益1年、权益3年',
  `account_name` varchar(100) NOT NULL COMMENT '账户名称,从维度名称中提取,如传统',
  `security_code` varchar(20) DEFAULT NULL COMMENT '证券代码,从维度名称中提取或通过资产名称匹配TB0006获取',
  `dimension_name` varchar(200) NOT NULL COMMENT '原始维度名称,如001045OF--华夏可转债增强债券A',
  `asset_name` varchar(200) DEFAULT NULL COMMENT '资产名称,从维度名称中提取',
  `period_date` varchar(8) NOT NULL COMMENT '周期,格式YYYYMMDD',
  `market_value` decimal(28,10) DEFAULT 0 COMMENT '市值金额',
  `var_amount` decimal(28,10) DEFAULT 0 COMMENT 'VaR金额',
  `incremental_var_amount` decimal(28,10) DEFAULT 0 COMMENT '增量VaR金额',
  `cte_percentage` decimal(10,6) DEFAULT 0 COMMENT 'CTE百分比',
  `cte_amount` decimal(28,10) DEFAULT 0 COMMENT 'CTE金额',
  `component_var_percentage` decimal(10,6) DEFAULT 0 COMMENT '成份VaR百分比',
  `component_var_contribution_percentage` decimal(10,6) DEFAULT 0 COMMENT '成份VaR贡献百分比',
  `component_var_amount` decimal(28,10) DEFAULT 0 COMMENT '成分VaR金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_var_analysis` (`accounting_period`, `data_type`, `account_name`, `dimension_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0003-VaR值分析表';

-- TB0004 - Wind行业表
DROP TABLE IF EXISTS `t_ast_wind_industry`;
CREATE TABLE `t_ast_wind_industry` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `security_code` varchar(20) NOT NULL COMMENT '证券标识代码',
  `security_name` varchar(100) NOT NULL COMMENT '证券名称',
  `industry` varchar(50) DEFAULT NULL COMMENT 'Wind行业分类',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ast_wind_industry_period_code` (`accounting_period`,`security_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1334 DEFAULT CHARSET=utf8mb3 COMMENT='Wind行业表';

-- TB0005 - Wind评级表
DROP TABLE IF EXISTS `t_ast_wind_rating`;
CREATE TABLE `t_ast_wind_rating` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `security_code` varchar(20) NOT NULL COMMENT '证券标识代码',
  `security_name` varchar(100) NOT NULL COMMENT '证券简称',
  `entity_rating` varchar(10) DEFAULT NULL COMMENT '主体评级,与TB0006中的credit_rating字段使用相同的字典数据',
  `bond_rating` varchar(10) DEFAULT NULL COMMENT '债项评级,与TB0006中的credit_rating字段使用相同的字典数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ast_wind_rating_period_code` (`accounting_period`,`security_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0005 - Wind评级表';

-- TB0006 - 资产定义表
DROP TABLE IF EXISTS `t_ast_asset_definition`;
CREATE TABLE `t_ast_asset_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '原始账户名称,存储实际的原始文本,如传统、分红、万能、投连、资本补充债等',
  `asset_name` varchar(200) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券标识代码',
  `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类,01:存款,02:政府债券,03:中期票据,04:债券型基金,05:公募基金固定收益类专户,06:公司债企业债,07:固定收益类保险资产管理产品,08:货币类保险资产管理产品,09:不动产债权投资计划,10:基础设施债权投资计划,11:信托计划,12:上市普通股票,13:证券投资基金,14:可转债,15:以自有资金对保险类企业的股权投资,16:不含保证条款的股权投资计划、私募股权投资基金,17:权益类和混合类保险资管产品,18:权益类信托计划,19:不动产相关金融产品,20:权益类基金专户产品,21:REITS,22:融资回购,23:活期存款,24:回购,25:负债,26:其他,27:项目资产支持计划,28:短期、超短期融资券,29:其他普通未上市股权',
  `domestic_foreign` varchar(20) NOT NULL COMMENT '境内外标识,01:境内资产,02:境外资产,03:0（表示没有境内外这个属性）',
  `credit_rating` varchar(10) DEFAULT NULL COMMENT '信用评级,01:AA,02:AA+,03:AAA,04:不涉及,05:免评级,06:无评级',
  `industry_category` varchar(50) DEFAULT NULL COMMENT '所属行业分类',
  `alm_asset_name` varchar(100) DEFAULT NULL COMMENT 'ALM资产名称',
  `five_level_classification` varchar(20) DEFAULT NULL COMMENT '五级分类,01:正常类,02:关注类,03:次级类,04:可疑类,05:损失类,06:不良类',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accounting_period_account_name_asset_name_security_code` (`accounting_period`,`account_name`,`asset_name`,`security_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0006 - 资产定义表';

-- TB0007 - 账户名称映射表
DROP TABLE IF EXISTS `t_ast_account_name_map`;
CREATE TABLE `t_ast_account_name_map` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '原始账户名称,存储实际的原始文本,如传统、分红、万能、投连、资本补充债等',
  `account_name_mapping` varchar(50) NOT NULL COMMENT '映射后的标准账户名称,01:传统账户,02:分红账户,03:万能账户,04:独立账户,05:资本补充债账户,06:普通账户',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accounting_period_account_name` (`accounting_period`,`account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0007 - 账户名称映射表';

-- TB0008 - 资产基础配置表
DROP TABLE IF EXISTS `t_ast_asset_basic_config`;
CREATE TABLE `t_ast_asset_basic_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `sequence_number` int(10) NOT NULL COMMENT '配置序号',
  `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类,与TB0006中的asset_sub_sub_category字段保持一致',
  `fixed_income_sub_category` varchar(50) DEFAULT NULL COMMENT '固收资产细分类,01:传统固定收益类投资资产,02:非标准固定收益类投资资产,03:其他固定收益类金融产品',
  `calculable_cashflow_flag` char(5) NOT NULL DEFAULT '0' COMMENT '可计算现金流标识,0:否,1:是',
  `credit_rating_logic_flag` char(5) NOT NULL DEFAULT '0' COMMENT '信用评级取值逻辑标识',
  `industry_statistics_flag` varchar(20) NOT NULL COMMENT '行业统计标识,01:不考虑,02:银行,03:非银金融,04:wind中获取,05:房地产,06:建筑装饰',
  `single_asset_statistics_flag` varchar(20) NOT NULL COMMENT '单一资产统计标识,01:不考虑,02:考虑',
  `five_level_statistics_flag` varchar(20) NOT NULL COMMENT '五级分类资产统计标识,01:不考虑,02:债券,03:其他,04:基础设施及不动产债权投资计划,05:股权-无公允价格,06:股权金融产品,07:股权',
  `spread_duration_statistics_flag` char(5) NOT NULL DEFAULT '0' COMMENT '利差久期资产统计标识,0:否,1:是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accounting_period_sequence_asset_category` (`accounting_period`,`sequence_number`,`asset_sub_sub_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0008 - 资产基础配置表';

-- TB0009 - 资产配置状况分类表
DROP TABLE IF EXISTS `t_ast_asset_allocation_category`;
CREATE TABLE `t_ast_asset_allocation_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `asset_sub_sub_category` varchar(50) DEFAULT NULL COMMENT '资产小小类,与TB0006中的asset_sub_sub_category字段保持一致,分类层级记录时为空',
  `domestic_foreign` varchar(20) DEFAULT NULL COMMENT '境内外标识,与TB0006中的domestic_foreign字段保持一致,分类层级记录时为空',
  `category_id` varchar(20) NOT NULL COMMENT '分类唯一标识',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_category_id` varchar(20) DEFAULT NULL COMMENT '父级分类ID,顶级分类为空',
  `category_level` int(5) NOT NULL COMMENT '分类级别,1:资产大类,2:资产一级分类,3:资产二级分类,4:资产三级分类,5:资产小小类映射',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accounting_period_asset_domestic_category_id` (`accounting_period`,`asset_sub_sub_category`,`domestic_foreign`,`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0009 - 资产配置状况分类表';

-- TB0010 - 资产流动性分类及变现系数表
DROP TABLE IF EXISTS `t_ast_asset_liquidity_coeff`;
CREATE TABLE `t_ast_asset_liquidity_coeff` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类,与TB0006中的asset_sub_sub_category字段保持一致',
  `bond_type` varchar(20) DEFAULT NULL COMMENT '债券类型,01:国债,02:政府债,03:准政府债,04:政策性银行金融债,05:金融企业债,06:非金融企业债',
  `accounting_classification` varchar(20) DEFAULT NULL COMMENT '会计分类类型,01:交易类,02:可供出售类,03:持有到期类',
  `credit_rating` varchar(10) DEFAULT NULL COMMENT '信用评级,与TB0006中的credit_rating字段保持一致',
  `asset_liquidity_category` varchar(20) NOT NULL COMMENT '资产流动性分类,01:现金及流动性管理工具,02:高流动性资产,03:中低流动性资产,04:不涉及',
  `realization_coefficient` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '变现系数百分比,如100表示100%',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_liquidity_coeff` (`accounting_period`, `asset_sub_sub_category`, `bond_type`, `accounting_classification`, `credit_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0010 - 资产流动性分类及变现系数表';

-- TB0011 - 信用评级映射表
DROP TABLE IF EXISTS `t_ast_credit_rating_map`;
CREATE TABLE `t_ast_credit_rating_map` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `credit_rating` varchar(10) NOT NULL COMMENT '原始信用评级,与TB0006中的credit_rating字段使用相同的字典数据',
  `credit_rating_table_used` varchar(10) NOT NULL COMMENT '信用评级表使用的评级,与TB0006中的credit_rating字段使用相同的字典数据',
  `discount_curve_rating` varchar(10) NOT NULL COMMENT '折现曲线使用评级,与TB0006中的credit_rating字段使用相同的字典数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_credit_rating_map` (`accounting_period`, `credit_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0011 - 信用评级映射表';

-- TB0012 - 银行分类映射表
DROP TABLE IF EXISTS `t_ast_bank_classification_map`;
CREATE TABLE `t_ast_bank_classification_map` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `alm_asset_name` varchar(100) NOT NULL COMMENT 'ALM资产名称,与TB0006中的alm_asset_name字段保持一致',
  `bank_classification` varchar(50) NOT NULL COMMENT '银行分类,01:国有商业银行,02:股份制商业银行、邮政储蓄银行,需要创建新的字典数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bank_classification_map` (`accounting_period`, `alm_asset_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0012 - 银行分类映射表';

-- TB0013 - 付息方式映射表
DROP TABLE IF EXISTS `t_ast_payment_method_map`;
CREATE TABLE `t_ast_payment_method_map` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `annual_payment_frequency` int(5) NOT NULL COMMENT '年付息次数',
  `payment_method` varchar(20) NOT NULL COMMENT '付息方式描述,01:到期支付,02:按年支付,03:按半年支付,04:按季支付,需要创建新的字典数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_method_map` (`accounting_period`, `annual_payment_frequency`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0013 - 付息方式映射表';

-- TB0014 - 折现曲线配置表
DROP TABLE IF EXISTS `t_ast_discount_curve_config`;
CREATE TABLE `t_ast_discount_curve_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类,与TB0006中的asset_sub_sub_category字段保持一致',
  `discount_curve_rating` varchar(10) DEFAULT NULL COMMENT '折现曲线使用评级,与TB0011中的discount_curve_rating字段保持一致',
  `discount_curve_flag` int(5) NOT NULL COMMENT '折现曲线标识,0:适用到期收益率计算方法,1:国债曲线,2:企业债AAA曲线,3:企业债AA+曲线,4:企业债AA曲线,需要创建新的字典数据',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_discount_curve_config` (`accounting_period`, `asset_sub_sub_category`, `discount_curve_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0014 - 折现曲线配置表';

-- TB0015 - 固收资产剩余期限资产分类表
CREATE TABLE `t_ast_fixed_income_term_cat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类,与TB0006中的asset_sub_sub_category字段保持一致',
  `domestic_foreign` varchar(20) NOT NULL COMMENT '境内外标识,与TB0006中的domestic_foreign字段保持一致',
  `bond_type` varchar(20) DEFAULT NULL COMMENT '债券类型,与TB0010中的bond_type字段保持一致',
  `fixed_income_term_category` varchar(50) NOT NULL COMMENT '固收资产剩余期限分类,01:存款,02:（准）政府债券,03:金融企业（公司）债券,04:非金融企业（公司）债券,05:非标准固定收益类投资资产,06:其他固定收益类金融产品,07:境外固定收益类投资资产,需要创建新的字典数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_fixed_income_term_cat` (`account_period`, `asset_sub_sub_category`, `domestic_foreign`, `bond_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0015 - 固收资产剩余期限资产分类表';

-- TB0016 - 整体资产明细表
CREATE TABLE `t_ast_asset_detail_overall` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  -- 基础信息字段
  `accounting_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM（如202406）',
  `asset_number` int(11) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称,如传统账户、分红账户等,通过TB0007账户名称映射表转换',
  `security_code` varchar(20) DEFAULT NULL COMMENT '证券标识代码,来源于TB0006资产定义表',
  `asset_name` varchar(100) NOT NULL COMMENT '资产具体名称,来源于TB0006资产定义表',
  `asset_sub_sub_category` varchar(50) DEFAULT NULL COMMENT '资产小小类,来源于TB0006资产定义表',
  `asset_major_category` varchar(50) NOT NULL COMMENT '资产大类,通过TB0009资产配置状况分类表匹配',
  `domestic_foreign` varchar(20) DEFAULT NULL COMMENT '境内外标识,来源于TB0006资产定义表',
  -- 分类信息字段
  `fixed_income_sub_category` varchar(50) DEFAULT NULL COMMENT '固定收益资产细分,通过TB0008资产基础配置表匹配',
  `asset_allocation_level1` varchar(100) DEFAULT NULL COMMENT '资产配置状况一级分类',
  `asset_allocation_level2` varchar(100) DEFAULT NULL COMMENT '资产配置状况二级分类',
  `asset_allocation_level3` varchar(100) DEFAULT NULL COMMENT '资产配置状况三级分类',
  -- 评级信息字段
  `wind_entity_rating` varchar(10) DEFAULT NULL COMMENT 'Wind系统中的主体评级,通过TB0005Wind评级表匹配',
  `wind_debt_rating` varchar(10) DEFAULT NULL COMMENT 'Wind系统中的债项评级,通过TB0005Wind评级表匹配',
  `risk_entity_rating` varchar(10) DEFAULT NULL COMMENT '风控绩效系统主体评级,通过TB0002三账户持仓表匹配',
  `risk_debt_rating` varchar(10) DEFAULT NULL COMMENT '风控绩效系统债项评级,通过TB0002三账户持仓表匹配',
  `credit_rating_maintenance` varchar(10) DEFAULT NULL COMMENT '信用评级维护表评级,直接从TB0006资产定义表的信用评级字段获取',
  `credit_rating_logic_flag` varchar(5) DEFAULT NULL COMMENT '信用评级取值逻辑标识,通过TB0008资产基础配置表匹配',
  `credit_rating` varchar(10) DEFAULT NULL COMMENT '最终信用评级,根据信用评级取值逻辑标识计算',
  `credit_rating_category` varchar(20) DEFAULT NULL COMMENT '信用评级分类,通过TB0011信用评级映射表匹配',
  -- 持仓信息字段
  `holding_quantity` decimal(28,10) DEFAULT 0 COMMENT '持仓数量,通过TB0002三账户持仓表匹配',
  `holding_face_value` decimal(28,10) DEFAULT 0 COMMENT '持仓面值,通过TB0002三账户持仓表匹配',
  `cost` decimal(28,10) DEFAULT 0 COMMENT '成本金额,通过TB0002三账户持仓表匹配',
  `net_cost` decimal(28,10) DEFAULT 0 COMMENT '净价成本,通过TB0002三账户持仓表匹配',
  `net_market_value` decimal(28,10) DEFAULT 0 COMMENT '净价市值,通过TB0002三账户持仓表匹配',
  `market_value` decimal(28,10) DEFAULT 0 COMMENT '市值,通过TB0001组合持仓表匹配',
  `var_1_year` decimal(28,10) DEFAULT 0 COMMENT '1年风险价值,通过TB0003VaR值分析表匹配',
  `var_3_year` decimal(28,10) DEFAULT 0 COMMENT '3年风险价值,通过TB0003VaR值分析表匹配',
  `book_balance` decimal(28,10) DEFAULT 0 COMMENT '账面余额,等于市值',
  `asset_impairment_provision` decimal(28,10) DEFAULT 0 COMMENT '资产减值准备,赋值为0',
  `book_value` decimal(28,10) DEFAULT 0 COMMENT '账面价值,等于账面余额-资产减值准备',
  -- 期限和利率信息字段
  `coupon_rate` decimal(10,6) DEFAULT 0 COMMENT '票面利率,通过TB0002三账户持仓表匹配',
  `annual_payment_frequency` int(5) DEFAULT 0 COMMENT '年付息次数,通过TB0002三账户持仓表匹配',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '付息方式,通过TB0013付息方式映射表匹配',
  `value_date` date DEFAULT NULL COMMENT '起息日期,通过TB0002三账户持仓表匹配',
  `purchase_date` date DEFAULT NULL COMMENT '买入日期,通过TB0002三账户持仓表匹配',
  `maturity_date` date DEFAULT NULL COMMENT '到期日期,通过TB0002三账户持仓表匹配',
  `accounting_type` varchar(20) DEFAULT NULL COMMENT '会计分类类型,通过TB0002三账户持仓表匹配',
  -- 剩余期限计算字段
  `remaining_term` decimal(10,2) DEFAULT 0 COMMENT '剩余期限（年）,计算公式：ROUND((到期日-eomonth(账期转换日期,0))/365,2)',
  `remaining_term_category` int(5) DEFAULT 0 COMMENT '剩余期限分类标识,剩余期限>=0为0,剩余期限<0为1',
  `remaining_term_flag` varchar(50) DEFAULT NULL COMMENT '剩余期限标识描述,根据资产类型和剩余期限区间计算',
  -- 业务分类字段
  `industry_statistics_flag` varchar(20) DEFAULT NULL COMMENT '行业统计标识,先通过TB0008匹配,如为wind中获取则通过TB0004匹配',
  `bond_type` varchar(20) DEFAULT NULL COMMENT '债券类型,政府债券根据资产名称关键字判断,公司债企业债/中期票据根据行业统计标识判断',
  `fixed_income_term_category` varchar(50) DEFAULT NULL COMMENT '固收资产剩余期限分类,通过TB0015匹配',
  `alm_asset_name` varchar(100) DEFAULT NULL COMMENT 'ALM资产名称,先从TB0006匹配,否则从资产名称中提取',
  `bank_classification` varchar(50) DEFAULT NULL COMMENT '银行分类,通过TB0012银行分类映射表匹配',
  `five_level_classification` varchar(20) DEFAULT NULL COMMENT '五级分类,直接从TB0006资产定义表的五级分类字段获取',
  -- 标识字段
  `calculable_cashflow_flag` varchar(5) DEFAULT NULL COMMENT '可计算现金流标识,如剩余期限<0则为0,否则通过TB0008匹配',
  `spread_duration_statistics_flag` varchar(5) DEFAULT NULL COMMENT '利差久期资产统计标识,通过TB0008资产基础配置表匹配',
  `single_asset_statistics_flag` varchar(20) DEFAULT NULL COMMENT '单一资产统计标识,通过TB0008资产基础配置表匹配',
  `five_level_statistics_flag` varchar(20) DEFAULT NULL COMMENT '五级分类资产统计标识,通过TB0008资产基础配置表匹配',
  -- 流动性和折现相关字段
  `asset_liquidity_category` varchar(20) DEFAULT NULL COMMENT '资产流动性分类,政府债券按债券类型+会计分类匹配',
  `realization_coefficient` decimal(10,2) DEFAULT 0 COMMENT '变现系数,政府债券按债券类型+会计分类匹配',
  `discount_curve_rating` varchar(10) DEFAULT NULL COMMENT '折现曲线使用评级,通过TB0011信用评级映射表匹配',
  `discount_curve_flag` varchar(5) DEFAULT NULL COMMENT '折现曲线标识,如可计算现金流标识=0则不涉及,否则通过TB0014匹配',
  -- 调整日期字段
  `adjusted_value_date` date DEFAULT NULL COMMENT '调整后起息日,计算公式：eomonth(起息日,0)',
  `adjusted_purchase_date` date DEFAULT NULL COMMENT '调整后买入日,计算公式：eomonth(买入日,0)',
  `adjusted_maturity_date` date DEFAULT NULL COMMENT '调整后到期日,计算公式：eomonth(到期日,0)',
  -- 公共字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asset_detail_overall` (`accounting_period`, `asset_number`, `account_name`, `security_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0016 - 整体资产明细表';
