package com.xl.alm.job.adur.service;

import com.xl.alm.job.adur.entity.AdurDurationAssetDetailEntity;
import com.xl.alm.job.adur.entity.AdurKeyDurationDiscountFactorWithSpreadEntity;
import com.xl.alm.job.adur.service.impl.AdurDurationIndicatorCalculationServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DV10指标计算测试类
 * 
 * 测试DV10上升和下降情景的计算逻辑：
 * - DV10_X_上升 = 评估时点现金流值集向量*关键久期折现因子向量（压力方向=上升，关键期限=X）
 * - DV10_X_下降 = 评估时点现金流值集向量*关键久期折现因子向量（压力方向=下降，关键期限=X）
 * 
 * 匹配条件：
 * - 关键久期折现因子表含价差.压力方向=上升/下降
 * - 关键久期折现因子表含价差.关键期限=久期资产明细表列字段对应的关键期限
 * - 关键久期折现因子表含价差.账户名称=久期资产明细表.账户名称
 * - 关键久期折现因子表含价差.证券代码=久期资产明细表.证券代码
 */
@SpringBootTest
@ActiveProfiles("test")
public class AdurDurationIndicatorCalculationServiceDV10Test {

    @Test
    @DisplayName("测试DV10指标计算逻辑")
    void testDV10CalculationLogic() {
        // 创建测试数据
        AdurDurationAssetDetailEntity assetDetail = createTestAssetDetail();
        
        // 创建关键久期折现因子数据
        Map<String, Map<String, Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity>>> keyDurationDiscountFactorMap = 
            createTestKeyDurationDiscountFactorMap();
        
        // 创建评估时点现金流数据
        Map<Integer, BigDecimal> evalCashflowMap = createTestEvalCashflowMap();
        
        // 验证数据匹配逻辑
        String expectedCompositeKey = "BOND001|账户A|SEC001";
        assertTrue(keyDurationDiscountFactorMap.containsKey(expectedCompositeKey), 
                  "应该能够根据复合键找到关键久期折现因子数据");
        
        // 验证关键期限0的数据
        Map<String, Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity>> keyTermMap = 
            keyDurationDiscountFactorMap.get(expectedCompositeKey);
        assertNotNull(keyTermMap, "关键期限数据不应为空");
        
        Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity> stressDirectionMap = keyTermMap.get("0");
        assertNotNull(stressDirectionMap, "关键期限0的数据不应为空");
        
        // 验证上升和下降情景数据
        AdurKeyDurationDiscountFactorWithSpreadEntity upEntity = stressDirectionMap.get("上升");
        AdurKeyDurationDiscountFactorWithSpreadEntity downEntity = stressDirectionMap.get("下降");
        
        assertNotNull(upEntity, "上升情景数据不应为空");
        assertNotNull(downEntity, "下降情景数据不应为空");
        
        // 验证匹配条件
        assertEquals("BOND001", upEntity.getAssetNumber(), "资产编号应该匹配");
        assertEquals("账户A", upEntity.getAccountName(), "账户名称应该匹配");
        assertEquals("SEC001", upEntity.getSecurityCode(), "证券代码应该匹配");
        assertEquals("0", upEntity.getKeyTerm(), "关键期限应该匹配");
        assertEquals("上升", upEntity.getStressDirection(), "压力方向应该匹配");
        
        assertEquals("BOND001", downEntity.getAssetNumber(), "资产编号应该匹配");
        assertEquals("账户A", downEntity.getAccountName(), "账户名称应该匹配");
        assertEquals("SEC001", downEntity.getSecurityCode(), "证券代码应该匹配");
        assertEquals("0", downEntity.getKeyTerm(), "关键期限应该匹配");
        assertEquals("下降", downEntity.getStressDirection(), "压力方向应该匹配");
    }

    /**
     * 创建测试资产明细
     */
    private AdurDurationAssetDetailEntity createTestAssetDetail() {
        AdurDurationAssetDetailEntity assetDetail = new AdurDurationAssetDetailEntity();
        assetDetail.setAssetNumber("BOND001");
        assetDetail.setAccountName("账户A");
        assetDetail.setSecurityCode("SEC001");
        assetDetail.setAccountPeriod("202407");
        return assetDetail;
    }

    /**
     * 创建测试关键久期折现因子数据
     */
    private Map<String, Map<String, Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity>>> createTestKeyDurationDiscountFactorMap() {
        Map<String, Map<String, Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity>>> keyDurationDiscountFactorMap = new HashMap<>();
        
        // 构建复合键：资产编号+账户名称+证券代码
        String compositeKey = "BOND001|账户A|SEC001";
        
        // 创建关键期限0的上升情景数据
        AdurKeyDurationDiscountFactorWithSpreadEntity upEntity = new AdurKeyDurationDiscountFactorWithSpreadEntity();
        upEntity.setAssetNumber(1);
        upEntity.setAccountName("账户A");
        upEntity.setSecurityCode("SEC001");
        upEntity.setKeyTerm("0");
        upEntity.setStressDirection("上升");
        upEntity.setKeyDurationFactorWithSpreadSet("{\"0\":1.0,\"1\":0.99,\"2\":0.98,\"3\":0.97,\"12\":0.90}");
        
        // 创建关键期限0的下降情景数据
        AdurKeyDurationDiscountFactorWithSpreadEntity downEntity = new AdurKeyDurationDiscountFactorWithSpreadEntity();
        downEntity.setAssetNumber(1);
        downEntity.setAccountName("账户A");
        downEntity.setSecurityCode("SEC001");
        downEntity.setKeyTerm("0");
        downEntity.setStressDirection("下降");
        downEntity.setKeyDurationFactorWithSpreadSet("{\"0\":1.0,\"1\":1.01,\"2\":1.02,\"3\":1.03,\"12\":1.10}");
        
        // 构建数据结构
        Map<String, Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity>> keyTermMap = new HashMap<>();
        Map<String, AdurKeyDurationDiscountFactorWithSpreadEntity> stressDirectionMap = new HashMap<>();
        stressDirectionMap.put("上升", upEntity);
        stressDirectionMap.put("下降", downEntity);
        keyTermMap.put("0", stressDirectionMap);
        keyDurationDiscountFactorMap.put(compositeKey, keyTermMap);
        
        return keyDurationDiscountFactorMap;
    }

    /**
     * 创建测试评估时点现金流数据
     */
    private Map<Integer, BigDecimal> createTestEvalCashflowMap() {
        Map<Integer, BigDecimal> evalCashflowMap = new HashMap<>();
        evalCashflowMap.put(0, BigDecimal.ZERO);
        evalCashflowMap.put(1, new BigDecimal("1000"));
        evalCashflowMap.put(2, new BigDecimal("1000"));
        evalCashflowMap.put(3, new BigDecimal("1000"));
        evalCashflowMap.put(12, new BigDecimal("101000")); // 本金+利息
        return evalCashflowMap;
    }

    @Test
    @DisplayName("测试现值计算逻辑")
    void testPresentValueCalculation() {
        // 测试数据：现金流值集向量 * 折现因子向量
        Map<Integer, BigDecimal> cashflowMap = createTestEvalCashflowMap();
        
        // 上升情景折现因子：期限1=0.99, 期限2=0.98, 期限3=0.97, 期限12=0.90
        // 预期现值 = 1000*0.99 + 1000*0.98 + 1000*0.97 + 101000*0.90
        //         = 990 + 980 + 970 + 90900 = 93840
        BigDecimal expectedUpPresentValue = new BigDecimal("93840.000000");
        
        // 下降情景折现因子：期限1=1.01, 期限2=1.02, 期限3=1.03, 期限12=1.10  
        // 预期现值 = 1000*1.01 + 1000*1.02 + 1000*1.03 + 101000*1.10
        //         = 1010 + 1020 + 1030 + 111100 = 114160
        BigDecimal expectedDownPresentValue = new BigDecimal("114160.000000");
        
        System.out.println("预期上升情景现值: " + expectedUpPresentValue);
        System.out.println("预期下降情景现值: " + expectedDownPresentValue);
        
        // 验证计算逻辑符合要求：DV10_X_上升/下降 = 评估时点现金流值集向量*关键久期折现因子向量
        assertTrue(expectedUpPresentValue.compareTo(BigDecimal.ZERO) > 0, "上升情景现值应该大于0");
        assertTrue(expectedDownPresentValue.compareTo(BigDecimal.ZERO) > 0, "下降情景现值应该大于0");
        assertTrue(expectedDownPresentValue.compareTo(expectedUpPresentValue) > 0, "下降情景现值应该大于上升情景现值");
    }
}
