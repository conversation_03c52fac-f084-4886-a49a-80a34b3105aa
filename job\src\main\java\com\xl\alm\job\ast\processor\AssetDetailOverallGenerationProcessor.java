package com.xl.alm.job.ast.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xl.alm.job.ast.task.AssetDetailOverallGenerationTask;
import com.xl.alm.job.common.util.ProcessResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.PostConstruct;

/**
 * 整体资产明细表生成处理器
 * 对应UC0013：整体资产明细表生成
 *
 * 处理流程：
 * 1. 验证任务参数（账期）
 * 2. 检查基础数据完整性（TB0006等表）
 * 3. 清理指定账期的历史数据
 * 4. 通过复杂SQL整合各基础表数据生成整体资产明细
 * 5. 执行业务逻辑计算（信用评级计算、期限分类等）
 * 6. 批量插入生成的数据到TB0016表
 * 7. 数据验证和统计
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class AssetDetailOverallGenerationProcessor implements BasicProcessor {

    @Autowired
    private AssetDetailOverallGenerationTask assetDetailOverallGenerationTask;

    @Override
    public ProcessResult process(TaskContext taskContext) {
        log.info("整体资产明细表生成处理器开始执行，任务参数：{}", taskContext.getJobParams());

        try {
            // 解析任务参数
            JSONObject params = JSON.parseObject(taskContext.getJobParams());
            String accountingPeriod = params.getString("accountingPeriod");

            if (accountingPeriod == null || accountingPeriod.isEmpty()) {
                log.error("任务参数错误，缺少accountingPeriod参数");
                return ProcessResultUtil.fail("任务参数错误，缺少accountingPeriod参数");
            }

            // 执行整体资产明细表生成任务
            log.info("开始执行整体资产明细表生成，账期：{}", accountingPeriod);
            boolean result = assetDetailOverallGenerationTask.executeGeneration(accountingPeriod);

            if (!result) {
                log.error("整体资产明细表生成失败，账期：{}", accountingPeriod);
                return ProcessResultUtil.fail("整体资产明细表生成失败");
            }

            log.info("整体资产明细表生成处理器执行成功，账期：{}", accountingPeriod);
            return ProcessResultUtil.success("整体资产明细表生成成功");

        } catch (Exception e) {
            log.error("整体资产明细表生成处理器执行异常", e);
            return ProcessResultUtil.fail("整体资产明细表生成处理器执行异常：" + e.getMessage());
        }
    }

    /**
     * 用于本地测试的初始化方法
     */
    @PostConstruct
    public void init() {
        // 本地测试时取消注释
        TaskContext tc = new TaskContext();
        tc.setJobParams("{\"accountingPeriod\":\"202406\"}");
        try {
            process(tc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
