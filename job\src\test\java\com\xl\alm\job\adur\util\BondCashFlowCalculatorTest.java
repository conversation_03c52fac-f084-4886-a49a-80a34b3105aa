package com.xl.alm.job.adur.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 债券现金流计算工具类测试
 *
 * <AUTHOR> Assistant
 */
class BondCashFlowCalculatorTest {

    @Test
    @DisplayName("测试到期一次性支付现金流计算")
    void testMaturityPaymentCashFlow() {
        // 准备测试数据
        Integer assetNumber = 1;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.05"); // 5%年利率
        int paymentMethodCode = 0; // 到期一次性支付
        LocalDate adjustedValueDate = LocalDate.of(2024, 1, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 1, 1);
        LocalDate calculationBaseDate = LocalDate.of(2024, 1, 1);

        // 执行计算
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
            assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
            adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(assetNumber, result.getAssetNumber());
        assertEquals(0, result.getPaymentFrequency());
        assertEquals(12, result.getTotalPeriods());
        assertEquals(601, result.getCashFlows().size()); // 0-600期限，总共601期

        // 验证期限0现金流（基准日）
        BondCashFlowCalculator.CashFlowItem baseDateCashFlow = result.getCashFlows().get(0); // 期限0
        assertEquals(0, baseDateCashFlow.getPeriod());
        assertEquals("2024-01-01", baseDateCashFlow.getDate());
        assertEquals(BigDecimal.ZERO, baseDateCashFlow.getAmount());
        assertEquals("base_date", baseDateCashFlow.getType());

        // 验证到期日现金流（第12期）
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(12); // 第12期
        assertEquals(12, maturityCashFlow.getPeriod());
        assertEquals("2025-01-01", maturityCashFlow.getDate());
        // 到期一次性支付：本金 + 利息 = 100000 + 100000*0.05 = 105000
        assertEquals(new BigDecimal("105000.0000000000"), maturityCashFlow.getAmount());
        assertEquals("principal_and_interest", maturityCashFlow.getType());

        // 验证其他期间现金流为0
        for (int i = 0; i < 11; i++) {
            assertEquals(BigDecimal.ZERO, result.getCashFlows().get(i).getAmount());
            assertEquals("none", result.getCashFlows().get(i).getType());
        }
    }

    @Test
    @DisplayName("测试按年支付现金流计算")
    void testAnnualPaymentCashFlow() {
        // 准备测试数据
        Integer assetNumber = 2;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.06"); // 6%年利率
        int paymentMethodCode = 1; // 按年支付
        LocalDate adjustedValueDate = LocalDate.of(2024, 6, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2026, 6, 1);
        LocalDate calculationBaseDate = LocalDate.of(2024, 6, 1);

        // 执行计算
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
            assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
            adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getPaymentFrequency());
        assertEquals(24, result.getTotalPeriods());

        // 验证第一年付息（第12期，2025年6月）
        BondCashFlowCalculator.CashFlowItem firstInterest = result.getCashFlows().get(12); // 期限12
        assertEquals("2025-06-01", firstInterest.getDate());
        assertEquals(new BigDecimal("6000.0000000000"), firstInterest.getAmount()); // 100000 * 0.06
        assertEquals("interest", firstInterest.getType());

        // 验证到期日现金流（第24期，2026年6月）
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(24); // 期限24
        assertEquals("2026-06-01", maturityCashFlow.getDate());
        assertEquals(new BigDecimal("106000.0000000000"), maturityCashFlow.getAmount()); // 本金 + 利息
        assertEquals("principal_and_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试按半年支付现金流计算")
    void testSemiAnnualPaymentCashFlow() {
        // 准备测试数据
        Integer assetNumber = 3;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.04"); // 4%年利率
        int paymentMethodCode = 2; // 按半年支付
        LocalDate adjustedValueDate = LocalDate.of(2024, 3, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 3, 1);
        LocalDate calculationBaseDate = LocalDate.of(2024, 3, 1);

        // 执行计算
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
            assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
            adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getPaymentFrequency());
        assertEquals(12, result.getTotalPeriods());

        // 验证第一次付息（第6期，2024年9月）
        BondCashFlowCalculator.CashFlowItem firstInterest = result.getCashFlows().get(5);
        assertEquals("2024-09-01", firstInterest.getDate());
        assertEquals(new BigDecimal("2000.0000000000"), firstInterest.getAmount()); // 100000 * 0.04 / 2
        assertEquals("interest", firstInterest.getType());

        // 验证到期日现金流（第12期，2025年3月）
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(11);
        assertEquals("2025-03-01", maturityCashFlow.getDate());
        assertEquals(new BigDecimal("102000.0000000000"), maturityCashFlow.getAmount()); // 本金 + 半年利息
        assertEquals("principal_and_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试按季支付现金流计算")
    void testQuarterlyPaymentCashFlow() {
        // 准备测试数据
        Integer assetNumber = 4;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.08"); // 8%年利率
        int paymentMethodCode = 4; // 按季支付
        LocalDate adjustedValueDate = LocalDate.of(2024, 1, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2024, 7, 1);
        LocalDate calculationBaseDate = LocalDate.of(2024, 1, 1);

        // 执行计算
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
            assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
            adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.getPaymentFrequency());
        assertEquals(6, result.getTotalPeriods());

        // 验证第一次付息（第3期，2024年4月）
        BondCashFlowCalculator.CashFlowItem firstInterest = result.getCashFlows().get(2);
        assertEquals("2024-04-01", firstInterest.getDate());
        assertEquals(new BigDecimal("2000.0000000000"), firstInterest.getAmount()); // 100000 * 0.08 / 4
        assertEquals("interest", firstInterest.getType());

        // 验证到期日现金流（第6期，2024年7月）
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(5);
        assertEquals("2024-07-01", maturityCashFlow.getDate());
        assertEquals(new BigDecimal("102000.0000000000"), maturityCashFlow.getAmount()); // 本金 + 季度利息
        assertEquals("principal_and_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试非整数周期现金流计算")
    void testNonIntegerCycleCashFlow() {
        // 准备测试数据：13个月期限，按年支付
        Integer assetNumber = 5;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.06"); // 6%年利率
        int paymentMethodCode = 1; // 按年支付
        LocalDate adjustedValueDate = LocalDate.of(2024, 1, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 2, 1); // 13个月后
        LocalDate calculationBaseDate = LocalDate.of(2024, 1, 1);

        // 执行计算
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
            assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
            adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(13, result.getTotalPeriods());

        // 验证年度付息（第12期，2025年1月）
        BondCashFlowCalculator.CashFlowItem annualInterest = result.getCashFlows().get(11);
        assertEquals("2025-01-01", annualInterest.getDate());
        assertEquals(new BigDecimal("6000.0000000000"), annualInterest.getAmount()); // 年利息
        assertEquals("interest", annualInterest.getType());

        // 验证到期日现金流（第13期，2025年2月）
        // 应该是本金 + 1个月的利息
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(12);
        assertEquals("2025-02-01", maturityCashFlow.getDate());
        // 本金100000 + 1个月利息(100000 * 0.06 / 12 * 1) = 100500
        assertEquals(new BigDecimal("100500.0000000000"), maturityCashFlow.getAmount());
        assertEquals("principal_and_partial_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试JSON序列化和反序列化")
    void testJsonSerialization() {
        // 准备测试数据
        Integer assetNumber = 6;
        BigDecimal holdingFaceValue = new BigDecimal("50000");
        BigDecimal couponRate = new BigDecimal("0.03");
        int paymentMethodCode = 1;
        LocalDate adjustedValueDate = LocalDate.of(2024, 1, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 1, 1);
        LocalDate calculationBaseDate = LocalDate.of(2024, 1, 1);

        // 计算现金流
        BondCashFlowCalculator.CashFlowResult originalResult = BondCashFlowCalculator.calculateCashFlow(
            assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
            adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 序列化为JSON
        String json = BondCashFlowCalculator.toJson(originalResult);
        assertNotNull(json);
        assertFalse(json.isEmpty());
        assertTrue(json.contains(assetNumber.toString()));

        // 反序列化
        BondCashFlowCalculator.CashFlowResult deserializedResult = BondCashFlowCalculator.fromJson(json);
        assertNotNull(deserializedResult);
        assertEquals(originalResult.getAssetNumber(), deserializedResult.getAssetNumber());
        assertEquals(originalResult.getTotalPeriods(), deserializedResult.getTotalPeriods());
        assertEquals(originalResult.getPaymentFrequency(), deserializedResult.getPaymentFrequency());
        assertEquals(originalResult.getCashFlows().size(), deserializedResult.getCashFlows().size());
    }

    @Test
    @DisplayName("测试无效付息方式处理")
    void testInvalidPaymentMethod() {
        // 测试无效的付息方式代码
        assertThrows(IllegalArgumentException.class, () -> {
            BondCashFlowCalculator.PaymentMethod.fromCode(99);
        });
    }

    @Test
    @DisplayName("测试新格式JSON转换")
    void testNewFormatJsonConversion() {
        // 准备测试数据
        Integer assetNumber = 7;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.05"); // 5%
        int paymentMethodCode = 1; // 按年支付利息
        LocalDate adjustedValueDate = LocalDate.of(2023, 6, 30);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 6, 30);
        LocalDate calculationBaseDate = LocalDate.of(2023, 6, 30);

        // 计算现金流
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
                assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
                adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 转换为新格式JSON
        String json = BondCashFlowCalculator.toJson(result);
        assertNotNull(json);
        assertFalse(json.isEmpty());

        // 验证JSON格式包含新的字段
        assertTrue(json.contains("\"日期\""), "JSON应该包含'日期'字段");
        assertTrue(json.contains("\"值\""), "JSON应该包含'值'字段");

        // 使用CashFlowUtil解析JSON验证格式正确性
        Map<Integer, BigDecimal> amounts = CashFlowUtil.parseCashflowAmounts(json);
        assertNotNull(amounts);
        assertFalse(amounts.isEmpty());

        Map<Integer, String> dates = CashFlowUtil.parseCashflowDates(json);
        assertNotNull(dates);
        assertFalse(dates.isEmpty());

        // 验证期限0的现金流为0（起息日当天）
        assertTrue(amounts.containsKey(0));
        assertEquals(BigDecimal.ZERO, amounts.get(0));

        // 验证可以从JSON重新解析
        BondCashFlowCalculator.CashFlowResult parsedResult = BondCashFlowCalculator.fromJson(json);
        assertNotNull(parsedResult);
        assertNotNull(parsedResult.getCashFlows());
        assertFalse(parsedResult.getCashFlows().isEmpty());
    }

    @Test
    @DisplayName("测试新格式JSON解析")
    void testNewFormatJsonParsing() {
        // 创建测试JSON数据（新格式）
        String testJson = "{\"0\":{\"日期\":\"2023-06-30\",\"值\":\"0\"},\"12\":{\"日期\":\"2024-06-30\",\"值\":\"5000\"},\"24\":{\"日期\":\"2025-06-30\",\"值\":\"105000\"}}";

        // 解析JSON
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.fromJson(testJson);

        // 验证解析结果
        assertNotNull(result);
        assertNotNull(result.getCashFlows());
        assertEquals(3, result.getCashFlows().size());

        // 验证具体数据
        BondCashFlowCalculator.CashFlowItem item0 = result.getCashFlows().get(0);
        assertEquals(0, item0.getPeriod());
        assertEquals("2023-06-30", item0.getDate());
        assertEquals(BigDecimal.ZERO, item0.getAmount());

        BondCashFlowCalculator.CashFlowItem item12 = result.getCashFlows().get(1);
        assertEquals(12, item12.getPeriod());
        assertEquals("2024-06-30", item12.getDate());
        assertEquals(new BigDecimal("5000"), item12.getAmount());

        BondCashFlowCalculator.CashFlowItem item24 = result.getCashFlows().get(2);
        assertEquals(24, item24.getPeriod());
        assertEquals("2025-06-30", item24.getDate());
        assertEquals(new BigDecimal("105000"), item24.getAmount());
    }

    @Test
    @DisplayName("测试0-600期限完整范围现金流生成")
    void testFullTermRangeCashFlowGeneration() {
        // 准备测试数据
        Integer assetNumber = 8;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.05"); // 5%
        int paymentMethodCode = 1; // 按年支付利息
        LocalDate adjustedValueDate = LocalDate.of(2023, 6, 30);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 6, 30); // 2年期
        LocalDate calculationBaseDate = LocalDate.of(2023, 6, 30);

        // 计算现金流
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
                assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
                adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 验证基本信息
        assertNotNull(result);
        assertEquals(601, result.getCashFlows().size(), "应该生成0-600期限，总共601期现金流");

        // 验证期限0（基准日）
        BondCashFlowCalculator.CashFlowItem term0 = result.getCashFlows().get(0);
        assertEquals(0, term0.getPeriod(), "第一个现金流应该是期限0");
        assertEquals("2023-06-30", term0.getDate(), "期限0的日期应该是基准日");
        assertEquals(BigDecimal.ZERO, term0.getAmount(), "期限0的现金流通常为0");
        assertEquals("base_date", term0.getType(), "期限0的类型应该是base_date");

        // 验证期限12（第一年利息）
        BondCashFlowCalculator.CashFlowItem term12 = result.getCashFlows().get(12);
        assertEquals(12, term12.getPeriod(), "应该是期限12");
        assertEquals("2024-06-30", term12.getDate(), "期限12的日期应该正确");
        assertEquals(new BigDecimal("5000.0000000000"), term12.getAmount(), "期限12应该有利息现金流");
        assertEquals("interest", term12.getType(), "期限12的类型应该是interest");

        // 验证期限24（到期日）
        BondCashFlowCalculator.CashFlowItem term24 = result.getCashFlows().get(24);
        assertEquals(24, term24.getPeriod(), "应该是期限24");
        assertEquals("2025-06-30", term24.getDate(), "期限24的日期应该是到期日");
        assertEquals(new BigDecimal("105000.0000000000"), term24.getAmount(), "期限24应该有本金+利息");
        assertEquals("principal_and_interest", term24.getType(), "期限24的类型应该是principal_and_interest");

        // 验证期限600（最后一期）
        BondCashFlowCalculator.CashFlowItem term600 = result.getCashFlows().get(600);
        assertEquals(600, term600.getPeriod(), "最后一个现金流应该是期限600");
        assertEquals("2073-06-30", term600.getDate(), "期限600的日期应该是50年后");
        assertEquals(BigDecimal.ZERO, term600.getAmount(), "期限600超出到期日，现金流应该为0");
        assertEquals("none", term600.getType(), "期限600的类型应该是none");

        // 转换为JSON并验证格式
        String json = BondCashFlowCalculator.toJson(result);
        assertNotNull(json);
        assertTrue(json.contains("\"0\""), "JSON应该包含期限0");
        assertTrue(json.contains("\"12\""), "JSON应该包含期限12");
        assertTrue(json.contains("\"24\""), "JSON应该包含期限24");
        assertTrue(json.contains("\"600\""), "JSON应该包含期限600");

        // 使用CashFlowUtil验证解析
        Map<Integer, BigDecimal> amounts = CashFlowUtil.parseCashflowAmounts(json);
        assertEquals(601, amounts.size(), "解析后应该有601个期限的数据");
        assertTrue(amounts.containsKey(0), "应该包含期限0");
        assertTrue(amounts.containsKey(600), "应该包含期限600");
        assertEquals(BigDecimal.ZERO, amounts.get(0), "期限0的金额应该为0");
        assertEquals(new BigDecimal("5000.0000000000"), amounts.get(12), "期限12的金额应该正确");
    }

    @Test
    @DisplayName("测试现金流JSON按期限顺序排序")
    void testCashFlowJsonOrdering() {
        // 准备测试数据
        Integer assetNumber = 9;
        BigDecimal holdingFaceValue = new BigDecimal("100000");
        BigDecimal couponRate = new BigDecimal("0.04"); // 4%
        int paymentMethodCode = 1; // 按年支付利息
        LocalDate adjustedValueDate = LocalDate.of(2023, 1, 1);
        LocalDate adjustedMaturityDate = LocalDate.of(2025, 1, 1); // 2年期
        LocalDate calculationBaseDate = LocalDate.of(2023, 1, 1);

        // 计算现金流
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
                assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
                adjustedValueDate, adjustedMaturityDate, calculationBaseDate
        );

        // 转换为JSON
        String json = BondCashFlowCalculator.toJson(result);
        assertNotNull(json);
        assertFalse(json.isEmpty());

        // 验证JSON中期限的顺序
        int index0 = json.indexOf("\"0\":");
        int index1 = json.indexOf("\"1\":");
        int index12 = json.indexOf("\"12\":");
        int index24 = json.indexOf("\"24\":");
        int index100 = json.indexOf("\"100\":");
        int index600 = json.indexOf("\"600\":");

        // 验证关键期限都存在
        assertTrue(index0 > 0, "应该包含期限0");
        assertTrue(index1 > 0, "应该包含期限1");
        assertTrue(index12 > 0, "应该包含期限12");
        assertTrue(index24 > 0, "应该包含期限24");
        assertTrue(index100 > 0, "应该包含期限100");
        assertTrue(index600 > 0, "应该包含期限600");

        // 验证顺序：0 < 1 < 12 < 24 < 100 < 600
        assertTrue(index0 < index1, "期限0应该在期限1之前");
        assertTrue(index1 < index12, "期限1应该在期限12之前");
        assertTrue(index12 < index24, "期限12应该在期限24之前");
        assertTrue(index24 < index100, "期限24应该在期限100之前");
        assertTrue(index100 < index600, "期限100应该在期限600之前");

        // 验证JSON可以正确解析
        Map<Integer, BigDecimal> amounts = CashFlowUtil.parseCashflowAmounts(json);
        assertEquals(601, amounts.size(), "应该有601个期限的数据");

        // 验证关键期限的数据
        assertEquals(BigDecimal.ZERO, amounts.get(0), "期限0应该为0");
        assertEquals(new BigDecimal("4000.0000000000"), amounts.get(12), "期限12应该有利息");
        assertEquals(new BigDecimal("104000.0000000000"), amounts.get(24), "期限24应该有本金+利息");
        assertEquals(BigDecimal.ZERO, amounts.get(600), "期限600超出到期日应该为0");
    }

    @Test
    @DisplayName("测试字典值付息方式 - 年付")
    void testCalculateCashFlowWithDictValue_AnnualPayment() {
        // 测试年付（字典值"01"）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            101,
            new BigDecimal("1000000"),
            new BigDecimal("0.05"),
            "01", // 年付
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2026, 1, 1),
            LocalDate.of(2024, 1, 1)
        );

        assertNotNull(result);
        assertEquals("TEST001", result.getAssetNumber());
        assertEquals(1, result.getPaymentFrequency()); // 应该是1（对应字典值"01"）
        assertTrue(result.getCashFlows().size() > 0);
    }

    @Test
    @DisplayName("测试字典值付息方式 - 半年付")
    void testCalculateCashFlowWithDictValue_SemiAnnualPayment() {
        // 测试半年付（字典值"02"）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            102,
            new BigDecimal("1000000"),
            new BigDecimal("0.05"),
            "02", // 半年付
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2026, 1, 1),
            LocalDate.of(2024, 1, 1)
        );

        assertNotNull(result);
        assertEquals("TEST002", result.getAssetNumber());
        assertEquals(2, result.getPaymentFrequency()); // 应该是2（对应字典值"02"）
        assertTrue(result.getCashFlows().size() > 0);
    }

    @Test
    @DisplayName("测试字典值付息方式 - 季付")
    void testCalculateCashFlowWithDictValue_QuarterlyPayment() {
        // 测试季付（字典值"03"）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            103,
            new BigDecimal("1000000"),
            new BigDecimal("0.05"),
            "03", // 季付
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2026, 1, 1),
            LocalDate.of(2024, 1, 1)
        );

        assertNotNull(result);
        assertEquals("TEST003", result.getAssetNumber());
        assertEquals(3, result.getPaymentFrequency()); // 应该是3（对应字典值"03"）
        assertTrue(result.getCashFlows().size() > 0);
    }

    @Test
    @DisplayName("测试字典值付息方式 - 到期一次性付息")
    void testCalculateCashFlowWithDictValue_MaturityPayment() {
        // 测试到期一次性付息（字典值"05"）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            105,
            new BigDecimal("1000000"),
            new BigDecimal("0.05"),
            "05", // 到期一次性付息
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2026, 1, 1),
            LocalDate.of(2024, 1, 1)
        );

        assertNotNull(result);
        assertEquals("TEST005", result.getAssetNumber());
        assertEquals(5, result.getPaymentFrequency()); // 应该是5（对应字典值"05"）
        assertTrue(result.getCashFlows().size() > 0);
    }

    @Test
    @DisplayName("测试字典值付息方式 - 月付（暂不支持）")
    void testCalculateCashFlowWithDictValue_MonthlyPayment() {
        // 测试月付（字典值"04"，暂不支持，应该使用到期支付）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            104,
            new BigDecimal("1000000"),
            new BigDecimal("0.05"),
            "04", // 月付（暂不支持）
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2026, 1, 1),
            LocalDate.of(2024, 1, 1)
        );

        assertNotNull(result);
        assertEquals("TEST004", result.getAssetNumber());
        assertEquals(4, result.getPaymentFrequency()); // 应该是4（对应字典值"04"）
        assertTrue(result.getCashFlows().size() > 0);
    }

    @Test
    @DisplayName("测试到期支付计算逻辑")
    void testMaturityPaymentCalculation() {
        // 测试到期支付（字典值"05"）
        // 计算逻辑：现金流 = 持仓面值 × (1 + 票面利率/12 × 总月数)
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            106,
            new BigDecimal("100000"), // 持仓面值10万
            new BigDecimal("0.06"), // 票面利率6%
            "05", // 到期一次性付息
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2025, 1, 1), // 到期日（12个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals("TEST_MATURITY", result.getAssetNumber());
        assertEquals(5, result.getPaymentFrequency()); // 字典值"05"对应的整数值

        // 验证只有到期日有现金流
        long maturityCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(1, maturityCashFlowCount, "到期支付方式只应该在到期日有现金流");

        // 验证到期日现金流金额
        // 预期金额 = 100000 × (1 + 0.06/12 × 12) = 100000 × (1 + 0.06) = 106000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .findFirst()
            .orElse(null);

        assertNotNull(maturityCashFlow);
        assertEquals(new BigDecimal("106000.0000000000"), maturityCashFlow.getAmount());
        assertEquals("2025-01-01", maturityCashFlow.getDate());
    }

    @Test
    @DisplayName("测试按年支付计算逻辑 - 整数倍周期")
    void testAnnualPaymentCalculation_IntegerCycle() {
        // 测试按年支付（字典值"01"）- 24个月（2年，整数倍）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            107,
            new BigDecimal("100000"), // 持仓面值10万
            new BigDecimal("0.06"), // 票面利率6%
            "01", // 按年支付
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2026, 1, 1), // 到期日（24个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals(1, result.getPaymentFrequency()); // 字典值"01"

        // 验证有3个现金流：第12个月利息、第24个月本金+利息
        long positiveCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(2, positiveCashFlowCount, "按年支付应该有2个现金流：第12个月利息、第24个月本金+利息");

        // 验证第12个月利息现金流 = 100000 × 0.06 = 6000
        BondCashFlowCalculator.CashFlowItem interestCashFlow = result.getCashFlows().get(12);
        assertEquals(new BigDecimal("6000.0000000000"), interestCashFlow.getAmount());

        // 验证第24个月到期现金流 = 100000 × (1 + 0.06) = 106000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(24);
        assertEquals(new BigDecimal("106000.0000000000"), maturityCashFlow.getAmount());
    }
}
