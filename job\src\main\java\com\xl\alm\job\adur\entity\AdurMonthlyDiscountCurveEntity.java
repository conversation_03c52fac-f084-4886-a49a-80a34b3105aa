package com.xl.alm.job.adur.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xl.alm.job.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ADUR月度折现曲线表不含价差实体类
 * 对应表：t_adur_monthly_discount_curve
 * 用于UC0005：计算月度折现曲线不含价差
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_adur_monthly_discount_curve")
public class AdurMonthlyDiscountCurveEntity extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId
    @TableField("id")
    private Long id;

    /**
     * 账期
     */
    @TableField("account_period")
    private String accountPeriod;

    /**
     * 日期类型
     */
    @TableField("date_type")
    private String dateType;

    /**
     * 日期
     */
    @TableField("date")
    private Date date;

    /**
     * 资产编号
     */
    @TableField("asset_number")
    private Integer assetNumber;

    /**
     * 账户名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 资产名称
     */
    @TableField("asset_name")
    private String assetName;

    /**
     * 证券代码
     */
    @TableField("security_code")
    private String securityCode;

    /**
     * 折现曲线标识
     */
    @TableField("curve_id")
    private String curveId;

    /**
     * 月度折现曲线利率值集（JSON格式存储term_0到term_600的数据）
     */
    @TableField("monthly_discount_rate_set")
    private String monthlyDiscountRateSet;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_del")
    private Integer isDel = 0;
}
