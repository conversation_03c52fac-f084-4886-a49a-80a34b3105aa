package com.xl.alm.job.adur.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xl.alm.job.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ADUR月度折现因子表含价差实体类
 * 对应表：t_adur_monthly_discount_factor_with_spread
 * 用于UC0007：计算月度折现因子含价差
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_adur_monthly_discount_factor_with_spread")
public class AdurMonthlyDiscountFactorWithSpreadEntity extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId
    @TableField("id")
    private Long id;

    /**
     * 账期
     */
    @TableField("account_period")
    private String accountPeriod;

    /**
     * 久期类型
     */
    @TableField("duration_type")
    private String durationType;

    /**
     * 基点类型
     */
    @TableField("basis_point_type")
    private String basisPointType;

    /**
     * 日期类型
     */
    @TableField("date_type")
    private String dateType;

    /**
     * 日期
     */
    @TableField("date")
    private Date date;

    /**
     * 价差类型
     */
    @TableField("spread_type")
    private String spreadType;

    /**
     * 价差
     */
    @TableField("spread")
    private BigDecimal spread;

    /**
     * 曲线细分类
     */
    @TableField("curve_sub_category")
    private String curveSubCategory;

    /**
     * 资产编号
     */
    @TableField("asset_number")
    private Integer assetNumber;

    /**
     * 账户名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 资产名称
     */
    @TableField("asset_name")
    private String assetName;

    /**
     * 证券代码
     */
    @TableField("security_code")
    private String securityCode;

    /**
     * 折现曲线标识
     */
    @TableField("curve_id")
    private String curveId;

    /**
     * 月度折现因子表含价差值集（JSON格式存储term_0到term_600的数据）
     */
    @TableField("monthly_discount_factor_set")
    private String monthlyDiscountFactorSet;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_del")
    private Integer isDel = 0;

}
