package com.xl.alm.job.adur.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 付息方式字典值测试类
 *
 * 测试直接使用数据库字典值（01, 02, 03, 04）的现金流计算逻辑
 *
 * 付息方式映射关系：
 * - 字典值"01" -> 到期一次性支付（本金+利息）
 * - 字典值"02" -> 按年支付利息（每年固定月份付息）
 * - 字典值"03" -> 按半年支付利息（每半年固定月份付息）
 * - 字典值"04" -> 按季支付利息（每季度固定月份付息）
 */
public class PaymentMethodDictValueTest {

    @Test
    @DisplayName("测试字典值01 - 到期一次性付息")
    void testDictValue01_MaturityPayment() {
        // 测试数据：持仓面值100万，票面利率5%，期限12个月
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            1,
            new BigDecimal("1000000"), // 持仓面值100万
            new BigDecimal("0.05"), // 票面利率5%
            "01", // 到期一次性付息
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2025, 1, 1), // 到期日（12个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals(1, result.getPaymentFrequency()); // 字典值"01"对应的整数值

        // 验证只有到期日有现金流
        long positiveCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(1, positiveCashFlowCount, "到期支付方式只应该在到期日有现金流");

        // 验证到期日现金流金额
        // 计算逻辑：现金流 = 持仓面值 × (1 + 票面利率/12 × 总月数)
        // 预期金额 = 1000000 × (1 + 0.05/12 × 12) = 1000000 × (1 + 0.05) = 1050000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(12); // 第12个月
        assertEquals(new BigDecimal("1050000.0000000000"), maturityCashFlow.getAmount());
        assertEquals("2025-01-01", maturityCashFlow.getDate());
        assertEquals("principal_and_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试字典值02 - 按年支付（整数倍周期）")
    void testDictValue02_AnnualPayment_IntegerCycle() {
        // 测试数据：持仓面值100万，票面利率6%，期限24个月（2年，整数倍）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            2,
            new BigDecimal("1000000"), // 持仓面值100万
            new BigDecimal("0.06"), // 票面利率6%
            "02", // 按年支付
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2026, 1, 1), // 到期日（24个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals(2, result.getPaymentFrequency()); // 字典值"02"对应的整数值

        // 验证有2个现金流：第12个月利息、第24个月本金+利息
        long positiveCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(2, positiveCashFlowCount, "按年支付应该有2个现金流");

        // 验证第12个月利息现金流 = 1000000 × 0.06 = 60000
        BondCashFlowCalculator.CashFlowItem interestCashFlow = result.getCashFlows().get(12);
        assertEquals(new BigDecimal("60000.0000000000"), interestCashFlow.getAmount());
        assertEquals("interest", interestCashFlow.getType());

        // 验证第24个月到期现金流 = 1000000 × (1 + 0.06) = 1060000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(24);
        assertEquals(new BigDecimal("1060000.0000000000"), maturityCashFlow.getAmount());
        assertEquals("principal_and_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试字典值02 - 按年支付（非整数倍周期）")
    void testDictValue02_AnnualPayment_NonIntegerCycle() {
        // 测试数据：持仓面值100万，票面利率6%，期限18个月（1.5年，非整数倍）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            3,
            new BigDecimal("1000000"), // 持仓面值100万
            new BigDecimal("0.06"), // 票面利率6%
            "02", // 按年支付
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2025, 7, 1), // 到期日（18个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals(2, result.getPaymentFrequency()); // 字典值"02"对应的整数值

        // 验证有2个现金流：第12个月利息、第18个月本金+余数利息
        long positiveCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(2, positiveCashFlowCount, "按年支付应该有2个现金流");

        // 验证第12个月利息现金流 = 1000000 × 0.06 = 60000
        BondCashFlowCalculator.CashFlowItem interestCashFlow = result.getCashFlows().get(12);
        assertEquals(new BigDecimal("60000.0000000000"), interestCashFlow.getAmount());

        // 验证第18个月到期现金流 = 1000000 × (1 + 0.06/12 × 6) = 1000000 × (1 + 0.03) = 1030000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(18);
        assertEquals(new BigDecimal("1030000.0000000000"), maturityCashFlow.getAmount());
    }

    @Test
    @DisplayName("测试字典值03 - 按半年支付（整数倍周期）")
    void testDictValue03_SemiAnnualPayment_IntegerCycle() {
        // 测试数据：持仓面值100万，票面利率6%，期限12个月（2个半年，整数倍）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            4,
            new BigDecimal("1000000"), // 持仓面值100万
            new BigDecimal("0.06"), // 票面利率6%
            "03", // 按半年支付
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2025, 1, 1), // 到期日（12个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals(3, result.getPaymentFrequency()); // 字典值"03"对应的整数值

        // 验证有2个现金流：第6个月利息、第12个月本金+利息
        long positiveCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(2, positiveCashFlowCount, "按半年支付应该有2个现金流");

        // 验证第6个月利息现金流 = 1000000 × 0.06/2 = 30000
        BondCashFlowCalculator.CashFlowItem interestCashFlow = result.getCashFlows().get(6);
        assertEquals(new BigDecimal("30000.0000000000"), interestCashFlow.getAmount());
        assertEquals("interest", interestCashFlow.getType());

        // 验证第12个月到期现金流 = 1000000 × (1 + 0.06/2) = 1030000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(12);
        assertEquals(new BigDecimal("1030000.0000000000"), maturityCashFlow.getAmount());
        assertEquals("principal_and_interest", maturityCashFlow.getType());
    }

    @Test
    @DisplayName("测试字典值04 - 按季支付（整数倍周期）")
    void testDictValue04_QuarterlyPayment_IntegerCycle() {
        // 测试数据：持仓面值100万，票面利率8%，期限12个月（4个季度，整数倍）
        BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlowWithDictValue(
            5,
            new BigDecimal("1000000"), // 持仓面值100万
            new BigDecimal("0.08"), // 票面利率8%
            "04", // 按季支付
            LocalDate.of(2024, 1, 1), // 起息日
            LocalDate.of(2025, 1, 1), // 到期日（12个月后）
            LocalDate.of(2024, 1, 1)  // 计算基准日
        );

        assertNotNull(result);
        assertEquals(4, result.getPaymentFrequency()); // 字典值"04"对应的整数值

        // 验证有4个现金流：第3、6、9个月利息，第12个月本金+利息
        long positiveCashFlowCount = result.getCashFlows().stream()
            .filter(cf -> cf.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .count();
        assertEquals(4, positiveCashFlowCount, "按季支付应该有4个现金流");

        // 验证季度利息现金流 = 1000000 × 0.08/4 = 20000
        assertEquals(new BigDecimal("20000.0000000000"), result.getCashFlows().get(3).getAmount());
        assertEquals(new BigDecimal("20000.0000000000"), result.getCashFlows().get(6).getAmount());
        assertEquals(new BigDecimal("20000.0000000000"), result.getCashFlows().get(9).getAmount());

        // 验证第12个月到期现金流 = 1000000 × (1 + 0.08/4) = 1020000
        BondCashFlowCalculator.CashFlowItem maturityCashFlow = result.getCashFlows().get(12);
        assertEquals(new BigDecimal("1020000.0000000000"), maturityCashFlow.getAmount());
    }

}
